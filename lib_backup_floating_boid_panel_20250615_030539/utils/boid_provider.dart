import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../models/boid_entry.dart';

class BOIDProvider extends ChangeNotifier {
  List<BOIDEntry> _boidEntries = [];
  bool _isLoading = false;
  static const String _storageKey = 'nepali_results_boid_entries';

  // Getters
  List<BOIDEntry> get boidEntries => _boidEntries;
  bool get isLoading => _isLoading;

  // Constructor - Load BOIDs from storage
  BOIDProvider() {
    _loadBOIDs();
  }

  // Load BOIDs from SharedPreferences
  Future<void> _loadBOIDs() async {
    _setLoading(true);
    try {
      final prefs = await SharedPreferences.getInstance();
      final boidsJson = prefs.getStringList(_storageKey);
      
      if (boidsJson != null) {
        _boidEntries = boidsJson
            .map((boidJson) => BOIDEntry.fromJson(json.decode(boidJson)))
            .toList();
      }
    } catch (e) {
      debugPrint('Error loading BOIDs: $e');
    }
    _setLoading(false);
  }

  // Save BOIDs to SharedPreferences
  Future<void> _saveBOIDs() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final boidsJson = _boidEntries.map((boid) => json.encode(boid.toJson())).toList();
      await prefs.setStringList(_storageKey, boidsJson);
    } catch (e) {
      debugPrint('Error saving BOIDs: $e');
    }
  }

  // Set loading state
  void _setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }

  // Add a new BOID entry
  Future<bool> addBOID(String name, String boid) async {
    // Validate BOID (must be exactly 16 digits)
    if (boid.length != 16 || !RegExp(r'^\d{16}$').hasMatch(boid)) {
      return false;
    }

    // Check if BOID already exists
    if (_boidEntries.any((entry) => entry.boid == boid)) {
      return false;
    }

    final newEntry = BOIDEntry(name: name.trim(), boid: boid);
    _boidEntries.add(newEntry);
    await _saveBOIDs();
    notifyListeners();
    return true;
  }

  // Delete a BOID entry
  Future<void> deleteBOID(int index) async {
    if (index >= 0 && index < _boidEntries.length) {
      _boidEntries.removeAt(index);
      await _saveBOIDs();
      notifyListeners();
    }
  }

  // Delete BOID by BOID number
  Future<void> deleteBOIDByNumber(String boid) async {
    _boidEntries.removeWhere((entry) => entry.boid == boid);
    await _saveBOIDs();
    notifyListeners();
  }

  // Check if BOID exists
  bool boidExists(String boid) {
    return _boidEntries.any((entry) => entry.boid == boid);
  }

  // Get BOID entry by index
  BOIDEntry? getBOIDAt(int index) {
    if (index >= 0 && index < _boidEntries.length) {
      return _boidEntries[index];
    }
    return null;
  }
}
