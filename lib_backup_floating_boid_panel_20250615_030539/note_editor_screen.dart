import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:provider/provider.dart';
import '../models/note.dart';
import '../utils/notes_provider.dart';

class NoteEditorScreen extends StatefulWidget {
  final Note note;

  const NoteEditorScreen({super.key, required this.note});

  @override
  State<NoteEditorScreen> createState() => _NoteEditorScreenState();
}

class _NoteEditorScreenState extends State<NoteEditorScreen> {
  late TextEditingController _titleController;
  late TextEditingController _contentController;
  bool _isEditing = false;

  @override
  void initState() {
    super.initState();
    _titleController = TextEditingController(text: widget.note.title);
    _contentController = TextEditingController(text: widget.note.content);
  }

  @override
  void dispose() {
    _titleController.dispose();
    _contentController.dispose();
    super.dispose();
  }

  void _saveNote() {
    final notesProvider = Provider.of<NotesProvider>(context, listen: false);
    notesProvider.updateCurrentNote(
      title: _titleController.text,
      content: _contentController.text,
    );

    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Note saved successfully'),
        duration: Duration(seconds: 2),
      ),
    );

    setState(() {
      _isEditing = false;
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        toolbarHeight: kToolbarHeight + 24, // Add 3 units (24px) of height
        backgroundColor: Colors.transparent,
        elevation: 0,
        automaticallyImplyLeading: false,
        flexibleSpace: Container(
          padding: const EdgeInsets.only(top: 24), // Lower AppBar by 3 units
          alignment: Alignment.centerLeft,
          child: Row(
            children: [
              Padding(
                padding: const EdgeInsets.only(left: 8.0, top: 8.0), // Lower back arrow by 1 step
                child: IconButton(
                  icon: const Icon(Icons.arrow_back),
                  onPressed: () => Navigator.of(context).pop(),
                ),
              ),
              Expanded(
                child: Padding(
                  padding: const EdgeInsets.only(left: 8.0, right: 56.0), // Adjust text position
                  child: Text(
                    'Edit Note',
                    style: GoogleFonts.poppins(
                      fontWeight: FontWeight.bold,
                      fontSize: 20,
                    ),
                  ),
                ),
              ),
            ],
          ),
        ),
        actions: [
          Padding(
            padding: const EdgeInsets.only(top: 32.0), // Align with lowered AppBar + 1 step
            child: IconButton(
              icon: Icon(
                _isEditing ? Icons.save : Icons.edit,
                color: Theme.of(context).brightness == Brightness.dark
                    ? Colors.white
                    : Colors.black87, // Fix pencil icon visibility in light mode
              ),
              onPressed: () {
                if (_isEditing) {
                  _saveNote();
                } else {
                  setState(() {
                    _isEditing = true;
                  });
                }
              },
            ),
          ),
        ],
      ),
      // Notebook-style background
      body: Container(
        decoration: BoxDecoration(
          // Lined paper effect
          color:
              Theme.of(context).brightness == Brightness.dark
                  ? const Color(0xFF1E1E1E) // Dark mode background
                  : const Color(0xFFF8F8F8), // Light mode background
          // Adding horizontal lines with a decoration
          boxShadow: [
            for (double i = 0; i < 30; i++)
              BoxShadow(
                color:
                    Theme.of(context).brightness == Brightness.dark
                        ? Colors.white.withAlpha(8) // 0.03 * 255 ≈ 8
                        : Colors.black.withAlpha(13), // 0.05 * 255 ≈ 13
                offset: Offset(0, 24.0 * i),
                spreadRadius: 0.5,
                blurRadius: 0,
              ),
          ],
        ),
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Date display
              Align(
                alignment: Alignment.centerRight,
                child: Text(
                  '${widget.note.updatedAt.day}/${widget.note.updatedAt.month}/${widget.note.updatedAt.year}',
                  style: GoogleFonts.poppins(fontSize: 12, color: Colors.grey),
                ),
              ),

              const SizedBox(height: 16),

              // Title field
              TextField(
                controller: _titleController,
                enabled: _isEditing,
                style: GoogleFonts.poppins(
                  fontSize: 20,
                  fontWeight: FontWeight.bold,
                  color:
                      Theme.of(context).brightness == Brightness.dark
                          ? Colors.white
                          : Colors.black87,
                ),
                decoration: InputDecoration(
                  hintText: 'Note Title',
                  border:
                      _isEditing
                          ? const UnderlineInputBorder()
                          : InputBorder.none,
                  focusedBorder:
                      _isEditing
                          ? UnderlineInputBorder(
                            borderSide: BorderSide(
                              color: Theme.of(context).colorScheme.primary,
                            ),
                          )
                          : InputBorder.none,
                ),
              ),

              const SizedBox(height: 16),

              // Content field
              Expanded(
                child: TextField(
                  controller: _contentController,
                  enabled: _isEditing,
                  maxLines: null,
                  expands: true,
                  textAlignVertical: TextAlignVertical.top,
                  style: GoogleFonts.poppins(
                    fontSize: 16,
                    height: 1.5,
                    color:
                        Theme.of(context).brightness == Brightness.dark
                            ? Colors.grey[300]
                            : Colors.grey[800],
                  ),
                  decoration: InputDecoration(
                    hintText: 'Write your note here...',
                    border:
                        _isEditing
                            ? const OutlineInputBorder()
                            : InputBorder.none,
                    focusedBorder:
                        _isEditing
                            ? OutlineInputBorder(
                              borderSide: BorderSide(
                                color: Theme.of(context).colorScheme.primary,
                              ),
                            )
                            : InputBorder.none,
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
      floatingActionButton:
          _isEditing
              ? FloatingActionButton(
                onPressed: _saveNote,
                child: const Icon(Icons.save),
              )
              : null,
    );
  }
}
