import 'dart:async';
import 'package:flutter/foundation.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:http/http.dart' as http;
import 'package:html/parser.dart' as parser;
import 'models/notification_item.dart';
import 'utils/notification_provider.dart';

/// Service to check for new IPO results and send notifications
class IpoNotificationService {
  // Singleton pattern
  static final IpoNotificationService _instance = IpoNotificationService._internal();
  factory IpoNotificationService() => _instance;
  IpoNotificationService._internal();

  // Constants
  static const String _ipoResultUrl = 'https://iporesult.cdsc.com.np/';
  static const String _lastCheckedKey = 'last_checked_ipo';
  static const String _lastIpoCountKey = 'last_ipo_count';
  static const Duration _checkInterval = Duration(hours: 6);

  // State
  Timer? _timer;
  NotificationProvider? _notificationProvider;
  bool _isInitialized = false;

  /// Initialize the service
  Future<void> initialize(NotificationProvider notificationProvider) async {
    if (_isInitialized) return;
    
    _notificationProvider = notificationProvider;
    _startPeriodicCheck();
    _isInitialized = true;
    
    if (kDebugMode) {
      print('IPO Notification Service initialized');
    }
  }

  /// Start periodic checks for new IPO results
  void _startPeriodicCheck() {
    // Cancel any existing timer
    _timer?.cancel();
    
    // Check immediately
    _checkForNewIpoResults();
    
    // Set up periodic check
    _timer = Timer.periodic(_checkInterval, (_) {
      _checkForNewIpoResults();
    });
    
    if (kDebugMode) {
      print('IPO periodic check started with interval: $_checkInterval');
    }
  }

  /// Check for new IPO results
  Future<void> _checkForNewIpoResults() async {
    try {
      if (kDebugMode) {
        print('Checking for new IPO results...');
      }
      
      final prefs = await SharedPreferences.getInstance();
      final lastChecked = prefs.getString(_lastCheckedKey) ?? '';
      final lastIpoCount = prefs.getInt(_lastIpoCountKey) ?? 0;
      
      // Fetch the IPO result page
      final response = await http.get(Uri.parse(_ipoResultUrl));
      
      if (response.statusCode == 200) {
        // Parse the HTML
        final document = parser.parse(response.body);
        
        // Find all IPO result entries (this is a simplified example)
        // In a real implementation, you would need to parse the actual HTML structure
        final ipoEntries = document.querySelectorAll('.ipo-entry');
        
        if (kDebugMode) {
          print('Found ${ipoEntries.length} IPO entries');
        }
        
        // If there are more entries than last time, there are new results
        if (ipoEntries.length > lastIpoCount) {
          // Create a notification
          final notification = NotificationItem(
            id: DateTime.now().millisecondsSinceEpoch.toString(),
            title: 'New IPO Result Available',
            message: 'A new IPO result has been published. Check now!',
            timestamp: DateTime.now(),
            category: 'ipo',
            url: _ipoResultUrl,
            isRead: false,
          );
          
          // Add the notification
          _notificationProvider?.addNotification(notification);
          
          if (kDebugMode) {
            print('New IPO notification sent');
          }
        }
        
        // Update the last checked time and count
        await prefs.setString(_lastCheckedKey, DateTime.now().toIso8601String());
        await prefs.setInt(_lastIpoCountKey, ipoEntries.length);
      }
    } catch (e) {
      if (kDebugMode) {
        print('Error checking for IPO results: $e');
      }
    }
  }

  /// Force check for new IPO results
  Future<void> checkNow() async {
    await _checkForNewIpoResults();
  }

  /// Dispose resources
  void dispose() {
    _timer?.cancel();
    _timer = null;
  }
}
