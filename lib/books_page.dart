import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'services/chrome_custom_tabs_service.dart';

class BooksPage extends StatefulWidget {
  const BooksPage({super.key});

  @override
  State<BooksPage> createState() => _BooksPageState();
}

class _BooksPageState extends State<BooksPage> {
  String? _selectedClass;
  String? _selectedMedium;
  final List<Map<String, dynamic>> _classes = [
    {
      'name': 'Class 10',
      'color': Colors.purple,
      'mediums': [
        {
          'name': 'English Medium',
          'url': 'asset:///assets/pdfs/class10/english/math_part1.pdf',
          'subjects': [
            {'name': 'Mathematics Part 1', 'url': 'asset:///assets/pdfs/class10/english/math_part1.pdf'},
            {'name': 'English', 'url': 'https://moecdc.gov.np/storage/gallery/1659418880.pdf'},
            {'name': 'Science', 'url': 'https://moecdc.gov.np/storage/gallery/1659418880.pdf'},
            {'name': 'Social Studies', 'url': 'https://moecdc.gov.np/storage/gallery/1659418880.pdf'},
          ]
        },
        {
          'name': 'Nepali Medium',
          'url': 'https://moecdc.gov.np/storage/gallery/1659418880.pdf',
          'subjects': [
            {'name': 'गणित भाग १', 'url': 'https://moecdc.gov.np/storage/gallery/1659418880.pdf'},
            {'name': 'नेपाली', 'url': 'https://moecdc.gov.np/storage/gallery/1659418880.pdf'},
            {'name': 'विज्ञान', 'url': 'https://moecdc.gov.np/storage/gallery/1659418880.pdf'},
            {'name': 'सामाजिक अध्ययन', 'url': 'https://moecdc.gov.np/storage/gallery/1659418880.pdf'},
          ]
        },
      ],
    },
    {
      'name': 'Class 11',
      'color': Colors.blue,
      'mediums': [
        {
          'name': 'English Medium',
          'url': 'https://tsz.com.np/class-11-english-book-pdf/',
          'subjects': [
            {'name': 'English', 'url': 'https://tsz.com.np/class-11-english-book-pdf/'},
            {'name': 'Mathematics', 'url': 'https://tsz.com.np/class-11-english-book-pdf/'},
            {'name': 'Physics', 'url': 'https://tsz.com.np/class-11-english-book-pdf/'},
            {'name': 'Chemistry', 'url': 'https://tsz.com.np/class-11-english-book-pdf/'},
            {'name': 'Biology', 'url': 'https://tsz.com.np/class-11-english-book-pdf/'},
            {'name': 'Computer Science', 'url': 'https://tsz.com.np/class-11-english-book-pdf/'},
          ]
        },
        {
          'name': 'Nepali Medium',
          'url': 'https://tsz.com.np/class-11-nepali-book-pdf/',
          'subjects': [
            {'name': 'नेपाली', 'url': 'https://tsz.com.np/class-11-nepali-book-pdf/'},
            {'name': 'सामाजिक अध्ययन', 'url': 'https://tsz.com.np/class-11-nepali-book-pdf/'},
            {'name': 'अर्थशास्त्र', 'url': 'https://tsz.com.np/class-11-nepali-book-pdf/'},
            {'name': 'व्यवसाय अध्ययन', 'url': 'https://tsz.com.np/class-11-nepali-book-pdf/'},
          ]
        },
      ],
    },
    {
      'name': 'Class 12',
      'color': Colors.green,
      'mediums': [
        {
          'name': 'English Medium',
          'url': 'https://tsz.com.np/class-12-english-book-pdf/',
          'subjects': [
            {'name': 'English', 'url': 'https://tsz.com.np/class-12-english-book-pdf/'},
            {'name': 'Mathematics', 'url': 'https://tsz.com.np/class-12-english-book-pdf/'},
            {'name': 'Physics', 'url': 'https://tsz.com.np/class-12-english-book-pdf/'},
            {'name': 'Chemistry', 'url': 'https://tsz.com.np/class-12-english-book-pdf/'},
            {'name': 'Biology', 'url': 'https://tsz.com.np/class-12-english-book-pdf/'},
            {'name': 'Computer Science', 'url': 'https://tsz.com.np/class-12-english-book-pdf/'},
          ]
        },
        {
          'name': 'Nepali Medium',
          'url': 'https://tsz.com.np/class-12-nepali-book-pdf/',
          'subjects': [
            {'name': 'नेपाली', 'url': 'https://tsz.com.np/class-12-nepali-book-pdf/'},
            {'name': 'सामाजिक अध्ययन', 'url': 'https://tsz.com.np/class-12-nepali-book-pdf/'},
            {'name': 'अर्थशास्त्र', 'url': 'https://tsz.com.np/class-12-nepali-book-pdf/'},
            {'name': 'व्यवसाय अध्ययन', 'url': 'https://tsz.com.np/class-12-nepali-book-pdf/'},
          ]
        },
      ],
    },
  ];

  Future<void> _launchChromeCustomTab(String url, String title) async {
    // Show loading indicator
    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Row(
            children: [
              const SizedBox(
                width: 20,
                height: 20,
                child: CircularProgressIndicator(strokeWidth: 2, color: Colors.white),
              ),
              const SizedBox(width: 16),
              Text('Opening $title...'),
            ],
          ),
          duration: const Duration(seconds: 2),
        ),
      );
    }

    try {
      await ChromeCustomTabsService.launchTextbookPDF(url, title);
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).hideCurrentSnackBar();
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Could not open $title. Please check your internet connection.'),
            backgroundColor: Colors.red.shade600,
            action: SnackBarAction(
              label: 'Retry',
              textColor: Colors.white,
              onPressed: () => _launchChromeCustomTab(url, title),
            ),
          ),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(
          'Textbooks',
          style: GoogleFonts.poppins(
            fontSize: 22,
            fontWeight: FontWeight.bold,
          ),
        ),
        backgroundColor: Colors.blue.shade700,
        foregroundColor: Colors.white,
      ),
      body: SafeArea(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Padding(
              padding: const EdgeInsets.all(16.0),
              child: Text(
                _selectedClass == null 
                    ? 'Select a Class' 
                    : _selectedMedium == null 
                        ? 'Class $_selectedClass' 
                        : 'Class $_selectedClass - $_selectedMedium',
                style: GoogleFonts.poppins(
                  fontSize: 20,
                  fontWeight: FontWeight.bold,
                  color: Theme.of(context).brightness == Brightness.dark 
                      ? Colors.white 
                      : Colors.black87,
                ),
              ),
            ),
            
            // Back button if class or medium is selected
            if (_selectedClass != null)
              Padding(
                padding: const EdgeInsets.only(left: 16.0, bottom: 16.0),
                child: ElevatedButton.icon(
                  onPressed: () {
                    setState(() {
                      if (_selectedMedium != null) {
                        _selectedMedium = null;
                      } else {
                        _selectedClass = null;
                      }
                    });
                  },
                  icon: const Icon(Icons.arrow_back),
                  label: Text(_selectedMedium != null ? 'Back to Mediums' : 'Back to Classes'),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.grey.shade700,
                    foregroundColor: Colors.white,
                  ),
                ),
              ),
            
            // Show subjects if both class and medium are selected
            if (_selectedClass != null && _selectedMedium != null)
              _buildSubjectsList()
            // Show mediums if only class is selected
            else if (_selectedClass != null)
              _buildMediumsList()
            // Class list if no class is selected
            else
              Expanded(
                child: ListView.builder(
                  padding: const EdgeInsets.symmetric(horizontal: 16),
                  itemCount: _classes.length,
                  itemBuilder: (context, index) {
                    final classData = _classes[index];
                    return _buildClassCard(classData);
                  },
                ),
              ),
          ],
        ),
      ),
    );
  }

  Widget _buildClassCard(Map<String, dynamic> classData) {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;
    final Color baseColor = classData['color'] as Color;
    
    return Container(
      margin: const EdgeInsets.only(bottom: 16.0),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            baseColor,
            baseColor.withOpacity(0.7),
          ],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: baseColor.withOpacity(0.4),
            blurRadius: 10,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          borderRadius: BorderRadius.circular(16),
          onTap: () {
            setState(() {
              _selectedClass = classData['name'] as String;
            });
          },
          child: Padding(
            padding: const EdgeInsets.all(20),
            child: Row(
              children: [
                Container(
                  width: 50,
                  height: 50,
                  decoration: BoxDecoration(
                    shape: BoxShape.circle,
                    color: Colors.white.withOpacity(0.2),
                    border: Border.all(color: Colors.white, width: 2),
                  ),
                  child: const Icon(Icons.school, color: Colors.white, size: 28),
                ),
                const SizedBox(width: 16),
                Text(
                  classData['name'],
                  style: GoogleFonts.poppins(
                    fontSize: 22,
                    fontWeight: FontWeight.bold,
                    color: Colors.white,
                    shadows: [
                      Shadow(
                        blurRadius: 3,
                        color: Colors.black.withOpacity(0.3),
                        offset: const Offset(0, 1),
                      ),
                    ],
                  ),
                ),
                const Spacer(),
                Container(
                  width: 40,
                  height: 40,
                  decoration: BoxDecoration(
                    shape: BoxShape.circle,
                    color: Colors.white.withOpacity(0.2),
                  ),
                  child: const Icon(
                    Icons.arrow_forward_ios_rounded,
                    color: Colors.white,
                    size: 20,
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildMediumButton(Map<String, dynamic> medium, Color color) {
    final bool isNepaliMedium = medium['name'].contains('Nepali');
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;

    return Padding(
      padding: const EdgeInsets.only(bottom: 16.0),
      child: Container(
        width: double.infinity,
        height: 70,
        decoration: BoxDecoration(
          gradient: LinearGradient(
            colors: isNepaliMedium 
                ? [Colors.red.shade700, Colors.red.shade900]
                : [Colors.blue.shade600, Colors.blue.shade800],
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
          ),
          borderRadius: BorderRadius.circular(16),
          boxShadow: [
            BoxShadow(
              color: isNepaliMedium 
                  ? Colors.red.withOpacity(0.3)
                  : Colors.blue.withOpacity(0.3),
              blurRadius: 8,
              offset: const Offset(0, 4),
            ),
          ],
        ),
        child: Material(
          color: Colors.transparent,
          child: InkWell(
            borderRadius: BorderRadius.circular(16),
            onTap: () {
              _launchChromeCustomTab(medium['url'], '${medium['name']} Books');
            },
            child: Padding(
              padding: const EdgeInsets.symmetric(horizontal: 20),
              child: Row(
                children: [
                  // Language indicator
                  Container(
                    width: 40,
                    height: 40,
                    decoration: BoxDecoration(
                      shape: BoxShape.circle,
                      color: Colors.white.withOpacity(0.2),
                      border: Border.all(color: Colors.white, width: 2),
                    ),
                    child: Center(
                      child: Text(
                        isNepaliMedium ? 'नेपाली' : 'EN',
                        style: TextStyle(
                          color: Colors.white,
                          fontSize: isNepaliMedium ? 12 : 14,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                  ),
                  const SizedBox(width: 16),
                  // Medium name
                  Expanded(
                    child: Text(
                      medium['name'],
                      style: GoogleFonts.poppins(
                        fontSize: 18,
                        fontWeight: FontWeight.w600,
                        color: Colors.white,
                        shadows: [
                          Shadow(
                            blurRadius: 3,
                            color: Colors.black.withOpacity(0.3),
                            offset: const Offset(0, 1),
                          ),
                        ],
                      ),
                    ),
                  ),
                  // Download icon
                  Container(
                    width: 36,
                    height: 36,
                    decoration: BoxDecoration(
                      shape: BoxShape.circle,
                      color: Colors.white.withOpacity(0.2),
                    ),
                    child: const Icon(
                      Icons.download_rounded,
                      color: Colors.white,
                      size: 20,
                    ),
                  ),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildMediumsList() {
    final selectedClassData = _classes.firstWhere((c) => c['name'] == _selectedClass);
    final mediums = selectedClassData['mediums'] as List<dynamic>;
    
    return Expanded(
      child: ListView(
        padding: const EdgeInsets.symmetric(horizontal: 16.0),
        children: [
          Padding(
            padding: const EdgeInsets.symmetric(vertical: 16.0),
            child: Text(
              'Select Medium for $_selectedClass',
              style: GoogleFonts.poppins(
                fontSize: 18,
                fontWeight: FontWeight.w600,
              ),
            ),
          ),
          ...mediums.map<Widget>((medium) {
            final bool isNepaliMedium = medium['name'].contains('Nepali');
            
            return Card(
              margin: const EdgeInsets.only(bottom: 12.0),
              elevation: 4,
              shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
              child: InkWell(
                onTap: () {
                  setState(() {
                    _selectedMedium = medium['name'];
                  });
                },
                borderRadius: BorderRadius.circular(12),
                child: Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: Row(
                    children: [
                      // Flag icon
                      Container(
                        width: 30,
                        height: 20,
                        decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(4),
                          color: isNepaliMedium ? Colors.red : Colors.blue,
                        ),
                        child: Center(
                          child: Text(
                            isNepaliMedium ? 'नेपाली' : 'EN',
                            style: const TextStyle(
                              color: Colors.white,
                              fontSize: 10,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ),
                      ),
                      const SizedBox(width: 16),
                      Text(
                        medium['name'],
                        style: GoogleFonts.poppins(
                          fontSize: 16,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                      const Spacer(),
                      const Icon(Icons.arrow_forward_ios, size: 16),
                    ],
                  ),
                ),
              ),
            );
          }).toList(),
          _buildWarningCard(),
        ],
      ),
    );
  }

  Widget _buildSubjectsList() {
    final selectedClassData = _classes.firstWhere((c) => c['name'] == _selectedClass);
    final selectedMediumData = (selectedClassData['mediums'] as List<dynamic>)
        .firstWhere((m) => m['name'] == _selectedMedium);
    final subjects = selectedMediumData['subjects'] as List<dynamic>;
    
    return Expanded(
      child: ListView(
        padding: const EdgeInsets.symmetric(horizontal: 16.0),
        children: [
          Padding(
            padding: const EdgeInsets.symmetric(vertical: 16.0),
            child: Text(
              'Select Subject',
              style: GoogleFonts.poppins(
                fontSize: 18,
                fontWeight: FontWeight.w600,
              ),
            ),
          ),
          ...subjects.map<Widget>((subject) {
            return Card(
              margin: const EdgeInsets.only(bottom: 12.0),
              elevation: 4,
              shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
              child: InkWell(
                onTap: () => _launchChromeCustomTab(
                  subject['url'] as String, 
                  '${_selectedClass} ${_selectedMedium} - ${subject['name']}'
                ),
                borderRadius: BorderRadius.circular(12),
                child: Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: Row(
                    children: [
                      const Icon(Icons.book, color: Colors.blue),
                      const SizedBox(width: 16),
                      Expanded(
                        child: Text(
                          subject['name'],
                          style: GoogleFonts.poppins(
                            fontSize: 16,
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                      ),
                      const SizedBox(width: 8),
                      const Icon(Icons.download),
                    ],
                  ),
                ),
              ),
            );
          }).toList(),
          _buildWarningCard(),
        ],
      ),
    );
  }

  Widget _buildWarningCard() {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 16.0),
      child: Card(
        color: Colors.amber.shade100,
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Row(
            children: [
              const Icon(Icons.info_outline, color: Colors.amber),
              const SizedBox(width: 12),
              Flexible(
                child: Text(
                  'Some books may take time to load due to source issues. We\'re working on it.',
                  style: GoogleFonts.poppins(fontSize: 14),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}