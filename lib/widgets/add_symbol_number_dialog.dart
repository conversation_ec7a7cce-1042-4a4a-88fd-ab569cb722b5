import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:provider/provider.dart';
import '../utils/symbol_number_provider.dart';
import '../models/symbol_number_entry.dart';

class AddSymbolNumberDialog extends StatefulWidget {
  final String resultType;

  const AddSymbolNumberDialog({
    super.key,
    required this.resultType,
  });

  @override
  State<AddSymbolNumberDialog> createState() => _AddSymbolNumberDialogState();
}

class _AddSymbolNumberDialogState extends State<AddSymbolNumberDialog> {
  final TextEditingController _nameController = TextEditingController();
  final TextEditingController _symbolNumberController = TextEditingController();
  final TextEditingController _dateOfBirthController = TextEditingController();
  final TextEditingController _registrationNoController = TextEditingController();
  final TextEditingController _examRollNoController = TextEditingController();
  final TextEditingController _facultyController = TextEditingController();
  final _formKey = GlobalKey<FormState>();
  bool _isLoading = false;

  @override
  void dispose() {
    _nameController.dispose();
    _symbolNumberController.dispose();
    _dateOfBirthController.dispose();
    _registrationNoController.dispose();
    _examRollNoController.dispose();
    _facultyController.dispose();
    super.dispose();
  }

  Future<void> _saveSymbolNumber() async {
    if (!_formKey.currentState!.validate()) return;

    setState(() {
      _isLoading = true;
    });

    final entry = SymbolNumberEntry(
      name: _nameController.text.trim(),
      symbolNumber: _symbolNumberController.text.trim(),
      dateOfBirth: _dateOfBirthController.text.trim().isEmpty
          ? null
          : _dateOfBirthController.text.trim(),
      registrationNo: _registrationNoController.text.trim().isEmpty
          ? null
          : _registrationNoController.text.trim(),
      examRollNo: _examRollNoController.text.trim().isEmpty
          ? null
          : _examRollNoController.text.trim(),
      faculty: null, // Not used for these result types
      resultType: widget.resultType,
    );

    final symbolNumberProvider = Provider.of<SymbolNumberProvider>(context, listen: false);
    final success = await symbolNumberProvider.addSymbolNumber(entry);

    setState(() {
      _isLoading = false;
    });

    if (success) {
      if (mounted) {
        Navigator.of(context).pop();
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('${widget.resultType} details saved successfully!'),
            backgroundColor: Colors.green,
            duration: const Duration(seconds: 2),
          ),
        );
      }
    } else {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Symbol number already exists!'),
            backgroundColor: Colors.red,
            duration: const Duration(seconds: 3),
          ),
        );
      }
    }
  }

  List<Widget> _buildFormFields() {
    final List<Widget> fields = [
      // Name field (always required)
      TextFormField(
        controller: _nameController,
        decoration: InputDecoration(
          labelText: 'Name',
          hintText: 'e.g., John Doe',
          prefixIcon: const Icon(Icons.person_outline),
          border: OutlineInputBorder(
            borderRadius: BorderRadius.circular(8),
          ),
        ),
        validator: (value) {
          if (value == null || value.trim().isEmpty) {
            return 'Please enter a name';
          }
          if (value.trim().length < 2) {
            return 'Name must be at least 2 characters';
          }
          return null;
        },
      ),
      const SizedBox(height: 16),

      // Symbol Number OR Registration Number field for TU, Symbol Number for others
      TextFormField(
        controller: _symbolNumberController,
        decoration: InputDecoration(
          labelText: widget.resultType == 'TU' ? 'Registration Number OR Symbol Number' : 'Symbol Number',
          hintText: widget.resultType == 'TU' ? 'Enter registration number or symbol number' : 'Enter symbol number',
          prefixIcon: const Icon(Icons.numbers),
          border: OutlineInputBorder(
            borderRadius: BorderRadius.circular(8),
          ),
        ),
        validator: (value) {
          if (value == null || value.trim().isEmpty) {
            return widget.resultType == 'TU'
                ? 'Please enter registration number or symbol number'
                : 'Please enter symbol number';
          }
          return null;
        },
      ),
    ];

    // Add specific fields based on result type
    switch (widget.resultType) {
      case 'SEE':
      case 'HSEB':
      case 'CTBT':
        // SEE, HSEB, CTBT Results → Name + Symbol Number + Date of Birth
        fields.addAll([
          const SizedBox(height: 16),
          TextFormField(
            controller: _dateOfBirthController,
            decoration: InputDecoration(
              labelText: 'Date of Birth',
              hintText: 'YYYY-MM-DD',
              prefixIcon: const Icon(Icons.calendar_today),
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(8),
              ),
            ),
            validator: (value) {
              if (value == null || value.trim().isEmpty) {
                return 'Please enter date of birth';
              }
              return null;
            },
          ),
        ]);
        break;

      case 'TU':
        // TU Results → Name + Symbol Number (no additional fields)
        break;

      case 'KU':
        // KU Results → Name + Symbol Number + Registration No + Date of Birth + Exam Roll No
        fields.addAll([
          const SizedBox(height: 16),
          TextFormField(
            controller: _registrationNoController,
            decoration: InputDecoration(
              labelText: 'Registration No',
              hintText: 'Enter registration number',
              prefixIcon: const Icon(Icons.app_registration),
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(8),
              ),
            ),
            validator: (value) {
              if (value == null || value.trim().isEmpty) {
                return 'Please enter registration number';
              }
              return null;
            },
          ),
          const SizedBox(height: 16),
          TextFormField(
            controller: _dateOfBirthController,
            decoration: InputDecoration(
              labelText: 'Date of Birth',
              hintText: 'YYYY-MM-DD',
              prefixIcon: const Icon(Icons.calendar_today),
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(8),
              ),
            ),
            validator: (value) {
              if (value == null || value.trim().isEmpty) {
                return 'Please enter date of birth';
              }
              return null;
            },
          ),
          const SizedBox(height: 16),
          TextFormField(
            controller: _examRollNoController,
            decoration: InputDecoration(
              labelText: 'Exam Roll No',
              hintText: 'Enter exam roll number',
              prefixIcon: const Icon(Icons.assignment_ind),
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(8),
              ),
            ),
            validator: (value) {
              if (value == null || value.trim().isEmpty) {
                return 'Please enter exam roll number';
              }
              return null;
            },
          ),
        ]);
        break;

      case 'PU':
        // PU Results → Name + Symbol Number + Registration No
        fields.addAll([
          const SizedBox(height: 16),
          TextFormField(
            controller: _registrationNoController,
            decoration: InputDecoration(
              labelText: 'Registration No',
              hintText: 'Enter registration number',
              prefixIcon: const Icon(Icons.app_registration),
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(8),
              ),
            ),
            validator: (value) {
              if (value == null || value.trim().isEmpty) {
                return 'Please enter registration number';
              }
              return null;
            },
          ),
        ]);
        break;

      case 'CTEVT':
        // CTEVT Results → Name + Symbol Number + Date of Birth
        fields.addAll([
          const SizedBox(height: 16),
          TextFormField(
            controller: _dateOfBirthController,
            decoration: InputDecoration(
              labelText: 'Date of Birth',
              hintText: 'YYYY-MM-DD',
              prefixIcon: const Icon(Icons.calendar_today),
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(8),
              ),
            ),
            validator: (value) {
              if (value == null || value.trim().isEmpty) {
                return 'Please enter date of birth';
              }
              return null;
            },
          ),
        ]);
        break;
    }

    return fields;
  }

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: Row(
        children: [
          Icon(
            Icons.add_circle_outline,
            color: Theme.of(context).colorScheme.primary,
          ),
          const SizedBox(width: 8),
          Text(
            'Add ${widget.resultType} Details',
            style: GoogleFonts.poppins(
              fontWeight: FontWeight.bold,
              fontSize: 18,
            ),
          ),
        ],
      ),
      content: Form(
        key: _formKey,
        child: SingleChildScrollView(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: _buildFormFields(),
          ),
        ),
      ),
      actions: [
        TextButton(
          onPressed: _isLoading ? null : () => Navigator.of(context).pop(),
          child: Text(
            'Cancel',
            style: GoogleFonts.poppins(),
          ),
        ),
        ElevatedButton(
          onPressed: _isLoading ? null : _saveSymbolNumber,
          style: ElevatedButton.styleFrom(
            backgroundColor: Theme.of(context).colorScheme.primary,
            foregroundColor: Colors.white,
          ),
          child: _isLoading
              ? const SizedBox(
                  width: 16,
                  height: 16,
                  child: CircularProgressIndicator(
                    strokeWidth: 2,
                    valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                  ),
                )
              : Text(
                  'Save',
                  style: GoogleFonts.poppins(fontWeight: FontWeight.w500),
                ),
        ),
      ],
    );
  }
}
