import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:google_fonts/google_fonts.dart';
import '../theme_provider.dart';
import '../utils/notification_provider.dart';
import '../date_display.dart';
import '../notification_screen.dart';
import '../about_page.dart';
import '../services/chrome_custom_tabs_service.dart';

class CommonHeader extends StatelessWidget {
  final String title;
  final String subtitle;
  final bool showTitleSection;

  const CommonHeader({
    super.key,
    required this.title,
    required this.subtitle,
    this.showTitleSection = true,
  });

  @override
  Widget build(BuildContext context) {
    return SafeArea(
      child: Column(
        children: [
          // Top bar with Nepali date and action buttons
          Container(
            width: double.infinity,
            decoration: BoxDecoration(
              color: Theme.of(context).brightness == Brightness.dark
                  ? Theme.of(context).primaryColor.withAlpha(80)
                  : Colors.lightBlue.shade200,
            ),
            child: Column(
              children: [
                // Add significant padding to move UI 2.5 times lower and avoid status bar overlap
                // Additional 0.75% adjustment for better spacing
                SizedBox(height: MediaQuery.of(context).padding.top * 2.5 + 24 + (MediaQuery.of(context).size.height * 0.0075)),
                // Combined Nepali date and action buttons row
                Container(
                  height: 60,
                  padding: const EdgeInsets.symmetric(horizontal: 16.0),
                child: Row(
                  children: [
                    // Nepali date display - takes available space
                    Expanded(
                      child: DateDisplay(isCompact: true),
                    ),
                    // Action buttons on the right
                    Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        // Notification Icon with Badge
                        Consumer<NotificationProvider>(
                          builder: (context, notificationProvider, _) {
                            return Stack(
                              alignment: Alignment.center,
                              children: [
                                IconButton(
                                  icon: const Icon(Icons.notifications_outlined),
                                  tooltip: 'Notifications',
                                  onPressed: () {
                                    Navigator.push(
                                      context,
                                      MaterialPageRoute(
                                        builder: (context) => const NotificationScreen(),
                                      ),
                                    );
                                  },
                                  splashRadius: 24,
                                ),
                                // Only show badge if notifications are enabled and count > 0
                                if (notificationProvider.areNotificationsEnabled &&
                                    notificationProvider.notificationCount > 0)
                                  Positioned(
                                    top: 8,
                                    right: 8,
                                    child: Container(
                                      padding: const EdgeInsets.all(2),
                                      decoration: BoxDecoration(
                                        color: Theme.of(context).colorScheme.error,
                                        shape: BoxShape.circle,
                                      ),
                                      constraints: const BoxConstraints(
                                        minWidth: 16,
                                        minHeight: 16,
                                      ),
                                      child: Center(
                                        child: Text(
                                          notificationProvider.notificationCount > 9
                                              ? '9+'
                                              : notificationProvider.notificationCount
                                                  .toString(),
                                          style: const TextStyle(
                                            color: Colors.white,
                                            fontSize: 10,
                                            fontWeight: FontWeight.bold,
                                          ),
                                          textAlign: TextAlign.center,
                                        ),
                                      ),
                                    ),
                                  ),
                              ],
                            );
                          },
                        ),
                        // Dark Mode Toggle
                        Consumer<ThemeProvider>(
                          builder: (context, themeProvider, _) {
                            return IconButton(
                              icon: Icon(
                                themeProvider.isDarkMode
                                    ? Icons.light_mode_outlined
                                    : Icons.dark_mode_outlined,
                                color: Theme.of(context).brightness == Brightness.dark
                                    ? Colors.white
                                    : Colors.black87,
                              ),
                              tooltip: themeProvider.isDarkMode ? 'Light Mode' : 'Dark Mode',
                              onPressed: () {
                                themeProvider.toggleTheme();
                              },
                              splashRadius: 24,
                            );
                          },
                        ),
                        // More Options Menu
                        PopupMenuButton<String>(
                          icon: Icon(
                            Icons.more_vert,
                            color: Theme.of(context).brightness == Brightness.dark
                                ? Colors.white
                                : Colors.black87,
                          ),
                          tooltip: 'More Options',
                          position: PopupMenuPosition.under,
                          offset: const Offset(0, 8),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(12),
                          ),
                          onSelected: (value) async {
                            switch (value) {
                              case 'about':
                                Navigator.push(
                                  context,
                                  MaterialPageRoute(builder: (context) => const AboutPage()),
                                );
                                break;
                              case 'terms':
                                try {
                                  await ChromeCustomTabsService.launch(
                                    'https://technokdapps.blogspot.com/2025/05/terms-and-conditions-body-font-family.html',
                                    title: 'Terms and Conditions',
                                  );
                                } catch (e) {
                                  if (context.mounted) {
                                    ScaffoldMessenger.of(context).showSnackBar(
                                      SnackBar(content: Text('Could not open Terms and Conditions: ${e.toString()}')),
                                    );
                                  }
                                }
                                break;
                              case 'privacy':
                                try {
                                  await ChromeCustomTabsService.launch(
                                    'https://technokdapps.blogspot.com/2025/05/terms-and-conditions-privacy-policy.html',
                                    title: 'Privacy Policy',
                                  );
                                } catch (e) {
                                  if (context.mounted) {
                                    ScaffoldMessenger.of(context).showSnackBar(
                                      SnackBar(content: Text('Could not open Privacy Policy: ${e.toString()}')),
                                    );
                                  }
                                }
                                break;
                            }
                          },
                          itemBuilder: (BuildContext context) {
                            return [
                              PopupMenuItem<String>(
                                value: 'about',
                                child: Row(
                                  mainAxisAlignment: MainAxisAlignment.start,
                                  children: [
                                    Icon(
                                      Icons.info_outline,
                                      size: 20,
                                      color: Theme.of(context).colorScheme.primary,
                                    ),
                                    const SizedBox(width: 12),
                                    Text(
                                      'About App',
                                      style: GoogleFonts.poppins(
                                        fontSize: 13,
                                        fontWeight: FontWeight.w500,
                                        color: Theme.of(context).textTheme.bodyLarge?.color,
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                              PopupMenuItem<String>(
                                value: 'terms',
                                child: Row(
                                  mainAxisAlignment: MainAxisAlignment.start,
                                  children: [
                                    Icon(
                                      Icons.description_outlined,
                                      size: 20,
                                      color: Theme.of(context).colorScheme.primary,
                                    ),
                                    const SizedBox(width: 12),
                                    Text(
                                      'Terms & Conditions',
                                      style: GoogleFonts.poppins(
                                        fontSize: 13,
                                        fontWeight: FontWeight.w500,
                                        color: Theme.of(context).textTheme.bodyLarge?.color,
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                              PopupMenuItem<String>(
                                value: 'privacy',
                                child: Row(
                                  mainAxisAlignment: MainAxisAlignment.start,
                                  children: [
                                    Icon(
                                      Icons.privacy_tip_outlined,
                                      size: 20,
                                      color: Theme.of(context).colorScheme.primary,
                                    ),
                                    const SizedBox(width: 12),
                                    Text(
                                      'Privacy Policy',
                                      style: GoogleFonts.poppins(
                                        fontSize: 13,
                                        fontWeight: FontWeight.w500,
                                        color: Theme.of(context).textTheme.bodyLarge?.color,
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                            ];
                          },
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
        // Title section (curved header) - only show if requested
        if (showTitleSection)
          Container(
            height: 120,
            decoration: BoxDecoration(
              gradient:
                  Theme.of(context).brightness == Brightness.dark
                      ? LinearGradient(
                        begin: Alignment.topLeft,
                        end: Alignment.bottomRight,
                        colors: [
                          const Color(
                            0xFF1A237E,
                          ).withAlpha(200), // Deep indigo
                          const Color(0xFF0D47A1).withAlpha(180), // Deep blue
                          const Color(
                            0xFF1A1A2E,
                          ).withAlpha(150), // Dark blue-black
                        ],
                        stops: const [0.0, 0.5, 1.0],
                      )
                      : LinearGradient(
                        begin: Alignment.topCenter,
                        end: Alignment.bottomCenter,
                        colors: [
                          Colors.lightBlue.shade200,
                          Colors.lightBlue.shade50,
                        ],
                      ),
              borderRadius: const BorderRadius.only(
                bottomLeft: Radius.circular(30),
                bottomRight: Radius.circular(30),
              ),
              boxShadow: [
                BoxShadow(
                  color:
                      Theme.of(context).brightness == Brightness.dark
                          ? const Color(0xFF3F51B5).withAlpha(
                            80,
                          ) // Indigo shadow
                          : Colors.black.withAlpha(20),
                  blurRadius:
                      Theme.of(context).brightness == Brightness.dark
                          ? 12
                          : 8,
                  spreadRadius:
                      Theme.of(context).brightness == Brightness.dark ? 2 : 0,
                  offset: const Offset(0, 4),
                ),
              ],
            ),
            child: Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Text(
                    title,
                    style: GoogleFonts.poppins(
                      fontSize:
                          Theme.of(context).brightness == Brightness.dark
                              ? 32
                              : 28,
                      fontWeight: FontWeight.bold,
                      letterSpacing:
                          Theme.of(context).brightness == Brightness.dark
                              ? 1.0
                              : 0.5,
                      color:
                          Theme.of(context).brightness == Brightness.dark
                              ? Colors.white
                              : Colors.blue.shade800,
                      shadows:
                          Theme.of(context).brightness == Brightness.dark
                              ? [
                                Shadow(
                                  color: const Color(
                                    0xFF2196F3,
                                  ).withAlpha(150),
                                  blurRadius: 3,
                                  offset: const Offset(0, 1),
                                ),
                              ]
                              : null,
                    ),
                  ),
                  const SizedBox(height: 8),
                  Text(
                    subtitle,
                    style: GoogleFonts.poppins(
                      fontSize: 16,
                      fontWeight:
                          Theme.of(context).brightness == Brightness.dark
                              ? FontWeight.w600
                              : FontWeight.w500,
                      letterSpacing: 0.5,
                      color:
                          Theme.of(context).brightness == Brightness.dark
                              ? Colors.white.withAlpha(220)
                              : Colors.blue.shade600,
                    ),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }
}
