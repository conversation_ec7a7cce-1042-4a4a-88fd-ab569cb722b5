import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:provider/provider.dart';
import '../utils/symbol_number_provider.dart';
import '../models/symbol_number_entry.dart';
import 'add_symbol_number_dialog.dart';

class SymbolNumberSaver extends StatelessWidget {
  final String resultType;

  const SymbolNumberSaver({
    super.key,
    required this.resultType,
  });

  void _copyToClipboard(BuildContext context, String text, String label) {
    Clipboard.setData(ClipboardData(text: text));
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('$label copied: $text'),
        duration: const Duration(seconds: 2),
        backgroundColor: Colors.green,
        behavior: SnackBarBehavior.floating,
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Consumer<SymbolNumberProvider>(
      builder: (context, symbolNumberProvider, child) {
        final entries = symbolNumberProvider.getSymbolNumbers(resultType);

        return Card(
          margin: const EdgeInsets.only(bottom: 16),
          child: Padding(
            padding: const EdgeInsets.all(16.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  '$resultType Symbol Number Saver',
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 8),
                Text(
                  'Save your $resultType details for quick access',
                  style: Theme.of(context).textTheme.bodyLarge,
                ),
                const SizedBox(height: 16),
                
                // Add Symbol Number Button
                SizedBox(
                  width: double.infinity,
                  child: ElevatedButton.icon(
                    icon: const Icon(Icons.add),
                    label: Text('Add $resultType Details'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: const Color(0xFF1976D2), // App primary blue
                      foregroundColor: Colors.white,
                    ),
                    onPressed: () {
                      showDialog(
                        context: context,
                        builder: (context) => AddSymbolNumberDialog(resultType: resultType),
                      );
                    },
                  ),
                ),
                
                // Saved Entries List
                if (entries.isNotEmpty) ...[
                  const SizedBox(height: 16),
                  Text(
                    'Saved $resultType Details:',
                    style: Theme.of(context).textTheme.titleSmall?.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 8),
                  ...entries.asMap().entries.map((entry) {
                    final index = entry.key;
                    final symbolEntry = entry.value;
                    return Card(
                      margin: const EdgeInsets.only(bottom: 8),
                      child: Padding(
                        padding: const EdgeInsets.all(12.0),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Row(
                              children: [
                                CircleAvatar(
                                  radius: 16,
                                  backgroundColor: const Color(0xFF1976D2).withValues(alpha: 0.2),
                                  child: Text(
                                    symbolEntry.name.isNotEmpty 
                                        ? symbolEntry.name[0].toUpperCase() 
                                        : 'S',
                                    style: const TextStyle(
                                      color: Color(0xFF1976D2),
                                      fontWeight: FontWeight.bold,
                                      fontSize: 12,
                                    ),
                                  ),
                                ),
                                const SizedBox(width: 12),
                                Expanded(
                                  child: Text(
                                    symbolEntry.name,
                                    style: GoogleFonts.poppins(
                                      fontWeight: FontWeight.w600,
                                      fontSize: 14,
                                    ),
                                  ),
                                ),
                                IconButton(
                                  icon: const Icon(Icons.delete, size: 18, color: Colors.red),
                                  onPressed: () {
                                    showDialog(
                                      context: context,
                                      builder: (context) => AlertDialog(
                                        title: const Text('Delete Entry'),
                                        content: Text('Are you sure you want to delete ${symbolEntry.name}\'s details?'),
                                        actions: [
                                          TextButton(
                                            onPressed: () => Navigator.of(context).pop(),
                                            child: const Text('Cancel'),
                                          ),
                                          TextButton(
                                            onPressed: () {
                                              symbolNumberProvider.deleteSymbolNumber(resultType, index);
                                              Navigator.of(context).pop();
                                              ScaffoldMessenger.of(context).showSnackBar(
                                                const SnackBar(
                                                  content: Text('Entry deleted successfully'),
                                                  duration: Duration(seconds: 2),
                                                ),
                                              );
                                            },
                                            child: const Text('Delete', style: TextStyle(color: Colors.red)),
                                          ),
                                        ],
                                      ),
                                    );
                                  },
                                  tooltip: 'Delete Entry',
                                ),
                              ],
                            ),
                            const SizedBox(height: 8),
                            
                            // Symbol Number or Registration Number for TU
                            _buildCopyableField(
                              context,
                              resultType == 'TU' ? 'Registration/Symbol Number' : 'Symbol Number',
                              symbolEntry.symbolNumber,
                              Icons.numbers,
                            ),
                            
                            // Date of Birth (for SEE, HSEB, CTEVT)
                            if (symbolEntry.dateOfBirth != null)
                              _buildCopyableField(
                                context,
                                'Date of Birth',
                                symbolEntry.dateOfBirth!,
                                Icons.calendar_today,
                              ),
                            
                            // Registration No (for KU, PU)
                            if (symbolEntry.registrationNo != null)
                              _buildCopyableField(
                                context,
                                'Registration No',
                                symbolEntry.registrationNo!,
                                Icons.app_registration,
                              ),
                            
                            // Exam Roll No (for KU)
                            if (symbolEntry.examRollNo != null)
                              _buildCopyableField(
                                context,
                                'Exam Roll No',
                                symbolEntry.examRollNo!,
                                Icons.assignment_ind,
                              ),
                          ],
                        ),
                      ),
                    );
                  }),
                ],
              ],
            ),
          ),
        );
      },
    );
  }

  Widget _buildCopyableField(
    BuildContext context,
    String label,
    String value,
    IconData icon,
  ) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 4),
      child: Row(
        children: [
          Icon(icon, size: 16, color: Colors.grey[600]),
          const SizedBox(width: 8),
          Text(
            '$label: ',
            style: GoogleFonts.poppins(
              fontSize: 12,
              fontWeight: FontWeight.w500,
              color: Colors.grey[600],
            ),
          ),
          Expanded(
            child: Text(
              value,
              style: GoogleFonts.robotoMono(
                fontSize: 12,
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
          IconButton(
            icon: const Icon(Icons.copy, size: 16),
            onPressed: () => _copyToClipboard(context, value, label),
            tooltip: 'Copy $label',
            constraints: const BoxConstraints(
              minWidth: 32,
              minHeight: 32,
            ),
          ),
        ],
      ),
    );
  }
}
