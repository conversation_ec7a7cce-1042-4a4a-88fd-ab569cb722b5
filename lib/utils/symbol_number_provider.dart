import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../models/symbol_number_entry.dart';

class SymbolNumberProvider extends ChangeNotifier {
  Map<String, List<SymbolNumberEntry>> _symbolNumbers = {};
  bool _isLoading = false;
  static const String _storageKey = 'nepali_results_symbol_numbers';

  // Getters
  Map<String, List<SymbolNumberEntry>> get symbolNumbers => _symbolNumbers;
  bool get isLoading => _isLoading;

  // Get symbol numbers for specific result type
  List<SymbolNumberEntry> getSymbolNumbers(String resultType) {
    return _symbolNumbers[resultType] ?? [];
  }

  // Constructor - Load symbol numbers from storage
  SymbolNumberProvider() {
    _loadSymbolNumbers();
  }

  // Load symbol numbers from SharedPreferences
  Future<void> _loadSymbolNumbers() async {
    _setLoading(true);
    try {
      final prefs = await SharedPreferences.getInstance();
      final symbolNumbersJson = prefs.getStringList(_storageKey);
      
      if (symbolNumbersJson != null) {
        final List<SymbolNumberEntry> allEntries = symbolNumbersJson
            .map((entryJson) => SymbolNumberEntry.fromJson(json.decode(entryJson)))
            .toList();
        
        // Group by result type
        _symbolNumbers = {};
        for (final entry in allEntries) {
          if (_symbolNumbers[entry.resultType] == null) {
            _symbolNumbers[entry.resultType] = [];
          }
          _symbolNumbers[entry.resultType]!.add(entry);
        }
      }
    } catch (e) {
      debugPrint('Error loading symbol numbers: $e');
    }
    _setLoading(false);
  }

  // Save symbol numbers to SharedPreferences
  Future<void> _saveSymbolNumbers() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final List<SymbolNumberEntry> allEntries = [];
      
      // Flatten all entries
      for (final entries in _symbolNumbers.values) {
        allEntries.addAll(entries);
      }
      
      final symbolNumbersJson = allEntries
          .map((entry) => json.encode(entry.toJson()))
          .toList();
      
      await prefs.setStringList(_storageKey, symbolNumbersJson);
    } catch (e) {
      debugPrint('Error saving symbol numbers: $e');
    }
  }

  // Set loading state
  void _setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }

  // Add a new symbol number entry
  Future<bool> addSymbolNumber(SymbolNumberEntry entry) async {
    // Check if entry already exists
    final existingEntries = _symbolNumbers[entry.resultType] ?? [];
    if (existingEntries.any((e) => e.symbolNumber == entry.symbolNumber)) {
      return false; // Already exists
    }

    // Add to the list
    if (_symbolNumbers[entry.resultType] == null) {
      _symbolNumbers[entry.resultType] = [];
    }
    _symbolNumbers[entry.resultType]!.add(entry);
    
    await _saveSymbolNumbers();
    notifyListeners();
    return true;
  }

  // Delete a symbol number entry
  Future<void> deleteSymbolNumber(String resultType, int index) async {
    final entries = _symbolNumbers[resultType];
    if (entries != null && index >= 0 && index < entries.length) {
      entries.removeAt(index);
      if (entries.isEmpty) {
        _symbolNumbers.remove(resultType);
      }
      await _saveSymbolNumbers();
      notifyListeners();
    }
  }

  // Delete symbol number by symbol number
  Future<void> deleteSymbolNumberByNumber(String resultType, String symbolNumber) async {
    final entries = _symbolNumbers[resultType];
    if (entries != null) {
      entries.removeWhere((entry) => entry.symbolNumber == symbolNumber);
      if (entries.isEmpty) {
        _symbolNumbers.remove(resultType);
      }
      await _saveSymbolNumbers();
      notifyListeners();
    }
  }

  // Check if symbol number exists
  bool symbolNumberExists(String resultType, String symbolNumber) {
    final entries = _symbolNumbers[resultType] ?? [];
    return entries.any((entry) => entry.symbolNumber == symbolNumber);
  }

  // Get symbol number entry by index
  SymbolNumberEntry? getSymbolNumberAt(String resultType, int index) {
    final entries = _symbolNumbers[resultType];
    if (entries != null && index >= 0 && index < entries.length) {
      return entries[index];
    }
    return null;
  }

  // Get total count for a result type
  int getCount(String resultType) {
    return _symbolNumbers[resultType]?.length ?? 0;
  }
}
