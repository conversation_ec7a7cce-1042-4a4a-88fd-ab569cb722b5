import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../models/dv_confirmation_entry.dart';

class DVConfirmationProvider extends ChangeNotifier {
  List<DVConfirmationEntry> _dvEntries = [];
  bool _isLoading = false;
  static const String _storageKey = 'nepali_results_dv_confirmations';

  // Getters
  List<DVConfirmationEntry> get dvEntries => _dvEntries;
  bool get isLoading => _isLoading;

  // Constructor - Load DV entries from storage
  DVConfirmationProvider() {
    _loadDVEntries();
  }

  // Load DV entries from SharedPreferences
  Future<void> _loadDVEntries() async {
    _setLoading(true);
    try {
      final prefs = await SharedPreferences.getInstance();
      final dvEntriesJson = prefs.getStringList(_storageKey);
      
      if (dvEntriesJson != null) {
        _dvEntries = dvEntriesJson
            .map((entryJson) => DVConfirmationEntry.fromJson(json.decode(entryJson)))
            .toList();
      }
    } catch (e) {
      debugPrint('Error loading DV entries: $e');
    }
    _setLoading(false);
  }

  // Save DV entries to SharedPreferences
  Future<void> _saveDVEntries() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final dvEntriesJson = _dvEntries.map((entry) => json.encode(entry.toJson())).toList();
      await prefs.setStringList(_storageKey, dvEntriesJson);
    } catch (e) {
      debugPrint('Error saving DV entries: $e');
    }
  }

  // Set loading state
  void _setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }

  // Add a new DV entry
  Future<bool> addDVEntry(DVConfirmationEntry entry) async {
    // Check if entry already exists
    if (_dvEntries.any((e) => e.confirmationNumber == entry.confirmationNumber)) {
      return false; // Already exists
    }

    _dvEntries.add(entry);
    await _saveDVEntries();
    notifyListeners();
    return true;
  }

  // Delete a DV entry
  Future<void> deleteDVEntry(int index) async {
    if (index >= 0 && index < _dvEntries.length) {
      _dvEntries.removeAt(index);
      await _saveDVEntries();
      notifyListeners();
    }
  }

  // Delete DV entry by confirmation number
  Future<void> deleteDVEntryByNumber(String confirmationNumber) async {
    _dvEntries.removeWhere((entry) => entry.confirmationNumber == confirmationNumber);
    await _saveDVEntries();
    notifyListeners();
  }

  // Check if confirmation number exists
  bool confirmationNumberExists(String confirmationNumber) {
    return _dvEntries.any((entry) => entry.confirmationNumber == confirmationNumber);
  }

  // Get DV entry by index
  DVConfirmationEntry? getDVEntryAt(int index) {
    if (index >= 0 && index < _dvEntries.length) {
      return _dvEntries[index];
    }
    return null;
  }

  // Get total count
  int get count => _dvEntries.length;
}
