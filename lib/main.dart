import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:firebase_core/firebase_core.dart';
import 'package:firebase_messaging/firebase_messaging.dart';
import 'theme_provider.dart';
import 'utils/notes_provider.dart';
import 'utils/notification_provider.dart';
import 'utils/boid_provider.dart';
import 'utils/symbol_number_provider.dart';
import 'utils/dv_confirmation_provider.dart';
import 'services/firebase_messaging_service.dart';
import 'main_layout.dart';
import 'splash_screen.dart';

// Global navigator key for accessing context from anywhere
final GlobalKey<NavigatorState> navigatorKey = GlobalKey<NavigatorState>();

void main() async {
  WidgetsFlutterBinding.ensureInitialized();
  await Firebase.initializeApp();

  // Register background message handler
  FirebaseMessaging.onBackgroundMessage(firebaseMessagingBackgroundHandler);

  // Initialize Firebase Messaging
  await FirebaseMessagingService.initialize();

  runApp(const MyApp());
}

class MyApp extends StatelessWidget {
  const MyApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MultiProvider(
      providers: [
        ChangeNotifierProvider(create: (context) => ThemeProvider()),
        ChangeNotifierProvider(create: (context) => NotesProvider()),
        ChangeNotifierProvider(create: (context) => NotificationProvider()),
        ChangeNotifierProvider(create: (context) => BOIDProvider()),
        ChangeNotifierProvider(create: (context) => SymbolNumberProvider()),
        ChangeNotifierProvider(create: (context) => DVConfirmationProvider()),
      ],
      child: Consumer<ThemeProvider>(
        builder: (context, themeProvider, child) {
          return MaterialApp(
            title: 'Nepali Results',
            debugShowCheckedModeBanner: false,
            navigatorKey: navigatorKey,
            theme: themeProvider.themeData,
            themeMode: themeProvider.isDarkMode ? ThemeMode.dark : ThemeMode.light,
            home: const SplashScreen(),
            // Remove default app icon on launch
            builder: (context, child) {
              return MediaQuery(
                // Remove padding to avoid default app icon
                data: MediaQuery.of(context).copyWith(padding: EdgeInsets.zero),
                child: child!,
              );
            },
          );
        },
      ),
    );
  }
}




