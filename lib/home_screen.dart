import 'package:flutter/material.dart';
import 'result_category.dart';
import 'constants.dart';
import 'grid_category_card.dart';
import 'result_page.dart';
import 'widgets/common_header.dart';

class HomeScreen extends StatefulWidget {
  const HomeScreen({super.key});

  @override
  State<HomeScreen> createState() => _HomeScreenState();
}

class _HomeScreenState extends State<HomeScreen> {
  late List<ResultCategory> _categories;

  @override
  void initState() {
    super.initState();
    _initializeCategories();
  }

  void _initializeCategories() {
    // Create categories from constants
    _categories =
        AppConstants.resultUrls.entries.map((entry) {
          final categoryName = entry.key;
          return ResultCategory(
            name: categoryName,
            description: AppConstants.categoryDescriptions[categoryName] ?? '',
            icon:
                AppConstants.categoryIcons[categoryName] ?? Icons.help_outline,
            resultUrls: entry.value,
            smsInfo: AppConstants.smsInfo[categoryName],
          );
        }).toList();

    // Sort categories to ensure specific order
    _categories.sort((a, b) {
      // Define the order of categories - IPO moved to second-last position
      const order = ['SEE', 'HSEB', 'TU', 'KU', 'PU', 'CTEVT', 'IPO', 'DV'];
      return order.indexOf(a.name).compareTo(order.indexOf(b.name));
    });
    
    // Note: We can't modify the description directly as it's final
    // The IPO description should be updated in the constants.dart file where categories are defined
  }

  @override
  void dispose() {
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      // Remove AppBar completely to avoid interfering with status bar
      appBar: null,
      body: Column(
        children: [
          // Use common header (which includes SafeArea)
          CommonHeader(
            title: AppConstants.appName,
            subtitle: "Check your results easily",
            showTitleSection: true,
          ),
          // Grid of result categories
          Expanded(
            child: Padding(
              padding: const EdgeInsets.all(16.0),
              child: GridView.builder(
                physics: const BouncingScrollPhysics(),
                gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
                  crossAxisCount:
                      MediaQuery.of(context).size.width > 600 ? 3 : 2,
                  childAspectRatio: 0.85,
                  crossAxisSpacing: 16,
                  mainAxisSpacing: 16,
                ),
                itemCount: _categories.length,
                itemBuilder: (context, index) {
                  final category = _categories[index];
                  return GridCategoryCard(
                    category: category,
                    onTap: () {
                      Navigator.push(
                        context,
                        MaterialPageRoute(
                          builder:
                              (context) => ResultPage(category: category),
                        ),
                      );
                    },
                  );
                },
              ),
            ),
          ),

        ],
      ),
      // Bottom navigation bar is now handled by MainLayout
    );
  }
}
