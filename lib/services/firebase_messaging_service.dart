import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:flutter/foundation.dart';
import 'package:provider/provider.dart';
import '../utils/notification_provider.dart';
import '../models/notification_item.dart';
import '../main.dart';

class FirebaseMessagingService {
  static final FirebaseMessaging _firebaseMessaging = FirebaseMessaging.instance;
  
  /// Initialize Firebase Messaging
  static Future<void> initialize() async {
    try {
      // Request permission for notifications
      NotificationSettings settings = await _firebaseMessaging.requestPermission(
        alert: true,
        announcement: false,
        badge: true,
        carPlay: false,
        criticalAlert: false,
        provisional: false,
        sound: true,
      );

      if (kDebugMode) {
        print('User granted permission: ${settings.authorizationStatus}');
      }

      // Get FCM token
      String? token = await _firebaseMessaging.getToken();
      if (kDebugMode) {
        print('FCM Token: $token');
      }

      // Handle token refresh
      _firebaseMessaging.onTokenRefresh.listen((String token) {
        if (kDebugMode) {
          print('FCM Token refreshed: $token');
        }
        // Here you can send the token to your server
      });

      // Handle foreground messages
      FirebaseMessaging.onMessage.listen(_handleForegroundMessage);

      // Handle background message taps
      FirebaseMessaging.onMessageOpenedApp.listen(_handleBackgroundMessageTap);

      // Handle initial message (when app is opened from terminated state)
      RemoteMessage? initialMessage = await _firebaseMessaging.getInitialMessage();
      if (initialMessage != null) {
        _handleBackgroundMessageTap(initialMessage);
      }

      if (kDebugMode) {
        print('Firebase Messaging initialized successfully');
      }
    } catch (e) {
      if (kDebugMode) {
        print('Error initializing Firebase Messaging: $e');
      }
    }
  }

  /// Handle foreground messages
  static void _handleForegroundMessage(RemoteMessage message) {
    if (kDebugMode) {
      print('Received foreground message: ${message.messageId}');
      print('Title: ${message.notification?.title}');
      print('Body: ${message.notification?.body}');
      print('Data: ${message.data}');
    }

    // Add notification to the local notification provider
    _addNotificationToProvider(message);
  }

  /// Handle background message taps
  static void _handleBackgroundMessageTap(RemoteMessage message) {
    if (kDebugMode) {
      print('Message clicked: ${message.messageId}');
      print('Data: ${message.data}');
    }

    // Add notification to the local notification provider
    _addNotificationToProvider(message);

    // Handle navigation based on message data
    _handleNotificationNavigation(message);
  }

  /// Add notification to the local notification provider
  static void _addNotificationToProvider(RemoteMessage message) {
    try {
      final context = navigatorKey.currentContext;
      if (context != null) {
        final notificationProvider = Provider.of<NotificationProvider>(context, listen: false);
        
        final notification = NotificationItem(
          id: message.messageId ?? DateTime.now().millisecondsSinceEpoch.toString(),
          title: message.notification?.title ?? 'New Notification',
          message: message.notification?.body ?? 'You have a new notification',
          timestamp: DateTime.now(),
          isRead: false,
          category: message.data['category'] ?? 'general',
          url: message.data['url'] ?? '',
        );

        notificationProvider.addNotification(notification);
      }
    } catch (e) {
      if (kDebugMode) {
        print('Error adding notification to provider: $e');
      }
    }
  }

  /// Handle notification navigation
  static void _handleNotificationNavigation(RemoteMessage message) {
    try {
      final context = navigatorKey.currentContext;
      if (context != null && message.data.isNotEmpty) {
        // Handle different types of navigation based on message data
        final String? action = message.data['action'];
        final String? url = message.data['url'];
        
        if (action != null) {
          switch (action) {
            case 'open_results':
              // Navigate to results page
              if (kDebugMode) {
                print('Navigating to results page');
              }
              break;
            case 'open_url':
              if (url != null) {
                // Open URL
                if (kDebugMode) {
                  print('Opening URL: $url');
                }
              }
              break;
            default:
              if (kDebugMode) {
                print('Unknown action: $action');
              }
          }
        }
      }
    } catch (e) {
      if (kDebugMode) {
        print('Error handling notification navigation: $e');
      }
    }
  }

  /// Get FCM token
  static Future<String?> getToken() async {
    try {
      return await _firebaseMessaging.getToken();
    } catch (e) {
      if (kDebugMode) {
        print('Error getting FCM token: $e');
      }
      return null;
    }
  }

  /// Subscribe to a topic
  static Future<void> subscribeToTopic(String topic) async {
    try {
      await _firebaseMessaging.subscribeToTopic(topic);
      if (kDebugMode) {
        print('Subscribed to topic: $topic');
      }
    } catch (e) {
      if (kDebugMode) {
        print('Error subscribing to topic $topic: $e');
      }
    }
  }

  /// Unsubscribe from a topic
  static Future<void> unsubscribeFromTopic(String topic) async {
    try {
      await _firebaseMessaging.unsubscribeFromTopic(topic);
      if (kDebugMode) {
        print('Unsubscribed from topic: $topic');
      }
    } catch (e) {
      if (kDebugMode) {
        print('Error unsubscribing from topic $topic: $e');
      }
    }
  }
}

/// Background message handler (must be top-level function)
@pragma('vm:entry-point')
Future<void> firebaseMessagingBackgroundHandler(RemoteMessage message) async {
  if (kDebugMode) {
    print('Handling background message: ${message.messageId}');
    print('Title: ${message.notification?.title}');
    print('Body: ${message.notification?.body}');
    print('Data: ${message.data}');
  }
}
