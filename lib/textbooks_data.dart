import '../models/textbook.dart';

class TextbooksData {
  static final List<TextbookClass> classes = [
    // Class 10 Textbooks
    TextbookClass(
      className: 'Class 10',
      mediums: [
        // Nepali Medium
        TextbookMedium(
          mediumName: 'Nepali Medium',
          textbooks: [
            Textbook(
              title: 'Nepali',
              pdfUrl: 'https://moecdc.gov.np/storage/gallery/1746444352.pdf',
              description: 'Class 10 Nepali Textbook',
            ),
            Textbook(
              title: 'Mathematics',
              pdfUrl: 'https://moecdc.gov.np/storage/gallery/1737352972.pdf',
              description: 'Class 10 Mathematics Textbook',
            ),
            Textbook(
              title: 'Science and Technology',
              pdfUrl: 'https://moecdc.gov.np/storage/gallery/1726588881.pdf',
              description: 'Class 10 Science and Technology Textbook',
            ),
            Textbook(
              title: 'Social Studies',
              pdfUrl: 'https://moecdc.gov.np/storage/gallery/1721237289.pdf',
              description: 'Class 10 Social Studies Textbook',
            ),
          ],
        ),

        // English Medium
        TextbookMedium(
          mediumName: 'English Medium',
          textbooks: [
            Textbook(
              title: 'English',
              pdfUrl: 'https://moecdc.gov.np/storage/gallery/1681716079.pdf',
              description: 'Class 10 English Textbook',
            ),
            Textbook(
              title: 'Mathematics Part 1',
              pdfUrl: 'https://moecdc.gov.np/storage/gallery/1730786225.pdf',
              description: 'Class 10 Mathematics Textbook Part 1',
            ),
            Textbook(
              title: 'Mathematics Part 2',
              pdfUrl: 'https://moecdc.gov.np/storage/gallery/1730786348.pdf',
              description: 'Class 10 Mathematics Textbook Part 2',
            ),
            Textbook(
              title: 'Science and Technology Part A',
              pdfUrl: 'https://moecdc.gov.np/storage/gallery/1730103584.pdf',
              description: 'Class 10 Science and Technology Textbook Part A',
            ),
            Textbook(
              title: 'Science and Technology Part B',
              pdfUrl: 'https://moecdc.gov.np/storage/gallery/1734001033.pdf',
              description: 'Class 10 Science and Technology Textbook Part B',
            ),
            Textbook(
              title: 'Science and Technology Part C',
              pdfUrl: 'https://moecdc.gov.np/storage/gallery/1730103812.pdf',
              description: 'Class 10 Science and Technology Textbook Part C',
            ),
          ],
        ),
      ],
    ),

    // Class 11 Textbooks
    TextbookClass(
      className: 'Class 11',
      mediums: [
        // English Medium
        TextbookMedium(
          mediumName: 'English Medium',
          textbooks: [
            Textbook(
              title: 'English',
              pdfUrl: 'https://moecdc.gov.np/storage/gallery/1673318499.pdf',
              description: 'Class 11 English Textbook',
            ),
            Textbook(
              title: 'Social Studies and Life Skills',
              pdfUrl: 'https://moecdc.gov.np/storage/gallery/1673318559.pdf',
              description: 'Class 11 Social Studies and Life Skills Textbook',
            ),
            Textbook(
              title: 'Mathematics',
              pdfUrl: 'https://moecdc.gov.np/storage/gallery/1673318510.pdf',
              description: 'Class 11 Mathematics Textbook',
            ),
            Textbook(
              title: 'Physics',
              pdfUrl: 'https://moecdc.gov.np/storage/gallery/1673318520.pdf',
              description: 'Class 11 Physics Textbook',
            ),
            Textbook(
              title: 'Chemistry',
              pdfUrl: 'https://moecdc.gov.np/storage/gallery/1673318530.pdf',
              description: 'Class 11 Chemistry Textbook',
            ),
            Textbook(
              title: 'Biology',
              pdfUrl: 'https://moecdc.gov.np/storage/gallery/1673318540.pdf',
              description: 'Class 11 Biology Textbook',
            ),
            Textbook(
              title: 'Computer Science',
              pdfUrl: 'https://moecdc.gov.np/storage/gallery/1673318550.pdf',
              description: 'Class 11 Computer Science Textbook',
            ),
          ],
        ),

        // Nepali Medium
        TextbookMedium(
          mediumName: 'Nepali Medium',
          textbooks: [
            Textbook(
              title: 'Nepali',
              pdfUrl: 'https://moecdc.gov.np/storage/gallery/1673318446.pdf',
              description: 'Class 11 Nepali Textbook',
            ),
            Textbook(
              title: 'Mathematics',
              pdfUrl: 'https://moecdc.gov.np/storage/gallery/1673318460.pdf',
              description: 'Class 11 Mathematics Textbook (Nepali Medium)',
            ),
            Textbook(
              title: 'Science',
              pdfUrl: 'https://moecdc.gov.np/storage/gallery/1673318470.pdf',
              description: 'Class 11 Science Textbook (Nepali Medium)',
            ),
          ],
        ),
      ],
    ),

    // Class 12 Textbooks
    TextbookClass(
      className: 'Class 12',
      mediums: [
        // English Medium
        TextbookMedium(
          mediumName: 'English Medium',
          textbooks: [
            Textbook(
              title: 'English',
              pdfUrl: 'https://moecdc.gov.np/storage/gallery/1673319459.pdf',
              description: 'Class 12 English Textbook',
            ),
            Textbook(
              title: 'Social Studies and Life Skills',
              pdfUrl: 'https://moecdc.gov.np/storage/gallery/1673319495.pdf',
              description: 'Class 12 Social Studies and Life Skills Textbook',
            ),
            Textbook(
              title: 'Mathematics',
              pdfUrl: 'https://moecdc.gov.np/storage/gallery/1673319510.pdf',
              description: 'Class 12 Mathematics Textbook',
            ),
            Textbook(
              title: 'Physics',
              pdfUrl: 'https://moecdc.gov.np/storage/gallery/1673319520.pdf',
              description: 'Class 12 Physics Textbook',
            ),
            Textbook(
              title: 'Chemistry',
              pdfUrl: 'https://moecdc.gov.np/storage/gallery/1673319530.pdf',
              description: 'Class 12 Chemistry Textbook',
            ),
            Textbook(
              title: 'Biology',
              pdfUrl: 'https://moecdc.gov.np/storage/gallery/1673319540.pdf',
              description: 'Class 12 Biology Textbook',
            ),
            Textbook(
              title: 'Computer Science',
              pdfUrl: 'https://moecdc.gov.np/storage/gallery/1673319550.pdf',
              description: 'Class 12 Computer Science Textbook',
            ),
          ],
        ),

        // Nepali Medium
        TextbookMedium(
          mediumName: 'Nepali Medium',
          textbooks: [
            Textbook(
              title: 'Nepali',
              pdfUrl: 'https://moecdc.gov.np/storage/gallery/1709443101.pdf',
              description: 'Class 12 Nepali Textbook',
            ),
            Textbook(
              title: 'Mathematics',
              pdfUrl: 'https://moecdc.gov.np/storage/gallery/1673319460.pdf',
              description: 'Class 12 Mathematics Textbook (Nepali Medium)',
            ),
            Textbook(
              title: 'Science',
              pdfUrl: 'https://moecdc.gov.np/storage/gallery/1673319470.pdf',
              description: 'Class 12 Science Textbook (Nepali Medium)',
            ),
          ],
        ),
      ],
    ),
  ];
}
