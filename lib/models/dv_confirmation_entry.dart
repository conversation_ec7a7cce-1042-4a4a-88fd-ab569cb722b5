class DVConfirmationEntry {
  final String fullName;
  final String confirmationNumber;
  final String lastName;
  final String birthYear;

  DVConfirmationEntry({
    required this.fullName,
    required this.confirmationNumber,
    required this.lastName,
    required this.birthYear,
  });

  // Convert to JSON for storage
  Map<String, dynamic> toJson() {
    return {
      'fullName': fullName,
      'confirmationNumber': confirmationNumber,
      'lastName': lastName,
      'birthYear': birthYear,
    };
  }

  // Create from JSON
  factory DVConfirmationEntry.fromJson(Map<String, dynamic> json) {
    return DVConfirmationEntry(
      fullName: json['fullName'] ?? '',
      confirmationNumber: json['confirmationNumber'] ?? '',
      lastName: json['lastName'] ?? '',
      birthYear: json['birthYear'] ?? '',
    );
  }

  @override
  String toString() {
    return 'DVConfirmationEntry{fullName: $fullName, confirmationNumber: $confirmationNumber}';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is DVConfirmationEntry && 
           other.fullName == fullName && 
           other.confirmationNumber == confirmationNumber;
  }

  @override
  int get hashCode => fullName.hashCode ^ confirmationNumber.hashCode;
}
