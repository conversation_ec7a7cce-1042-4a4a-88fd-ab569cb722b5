class BOIDEntry {
  final String name;
  final String boid;

  BOIDEntry({
    required this.name,
    required this.boid,
  });

  // Convert to JSON for storage
  Map<String, dynamic> toJson() {
    return {
      'name': name,
      'boid': boid,
    };
  }

  // Create from JSON
  factory BOIDEntry.fromJson(Map<String, dynamic> json) {
    return BOIDEntry(
      name: json['name'] ?? '',
      boid: json['boid'] ?? '',
    );
  }

  @override
  String toString() {
    return 'BOIDEntry{name: $name, boid: $boid}';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is BOIDEntry && other.name == name && other.boid == boid;
  }

  @override
  int get hashCode => name.hashCode ^ boid.hashCode;
}
