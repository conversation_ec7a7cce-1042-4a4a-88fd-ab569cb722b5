class SymbolNumberEntry {
  final String name;
  final String symbolNumber;
  final String? dateOfBirth;
  final String? registrationNo;
  final String? examRollNo;
  final String resultType; // SEE, HSEB, TU, KU, PU, CTEVT

  SymbolNumberEntry({
    required this.name,
    required this.symbolNumber,
    this.dateOfBirth,
    this.registrationNo,
    this.examRollNo,
    required this.resultType,
  });

  // Convert to JSON for storage
  Map<String, dynamic> toJson() {
    return {
      'name': name,
      'symbolNumber': symbolNumber,
      'dateOfBirth': dateOfBirth,
      'registrationNo': registrationNo,
      'examRollNo': examRollNo,
      'resultType': resultType,
    };
  }

  // Create from JSON
  factory SymbolNumberEntry.fromJson(Map<String, dynamic> json) {
    return SymbolNumberEntry(
      name: json['name'] ?? '',
      symbolNumber: json['symbolNumber'] ?? '',
      dateOfBirth: json['dateOfBirth'],
      registrationNo: json['registrationNo'],
      examRollNo: json['examRollNo'],
      resultType: json['resultType'] ?? '',
    );
  }

  @override
  String toString() {
    return 'SymbolNumberEntry{name: $name, symbolNumber: $symbolNumber, resultType: $resultType}';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is SymbolNumberEntry && 
           other.name == name && 
           other.symbolNumber == symbolNumber &&
           other.resultType == resultType;
  }

  @override
  int get hashCode => name.hashCode ^ symbolNumber.hashCode ^ resultType.hashCode;
}
