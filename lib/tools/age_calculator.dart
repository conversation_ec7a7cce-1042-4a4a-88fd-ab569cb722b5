import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:google_fonts/google_fonts.dart';

class AgeCalculator extends StatefulWidget {
  const AgeCalculator({super.key});

  @override
  State<AgeCalculator> createState() => _AgeCalculatorState();
}

class _AgeCalculatorState extends State<AgeCalculator> {
  final TextEditingController _dayController = TextEditingController();
  final TextEditingController _monthController = TextEditingController();
  final TextEditingController _yearController = TextEditingController();
  Map<String, dynamic>? _ageDetails;

  @override
  void initState() {
    super.initState();
  }

  @override
  void dispose() {
    _dayController.dispose();
    _monthController.dispose();
    _yearController.dispose();
    super.dispose();
  }

  void _calculateAge() {
    // Get values from text controllers
    final dayText = _dayController.text.trim();
    final monthText = _monthController.text.trim();
    final yearText = _yearController.text.trim();

    // Validate input
    if (dayText.isEmpty || monthText.isEmpty || yearText.isEmpty) {
      setState(() {
        _ageDetails = null;
      });
      return;
    }

    final day = int.tryParse(dayText);
    final month = int.tryParse(monthText);
    final year = int.tryParse(yearText);

    if (day == null || month == null || year == null) {
      setState(() {
        _ageDetails = null;
      });
      return;
    }

    // Validate date ranges
    if (day < 1 || day > 31 || month < 1 || month > 12 || year < 1900 || year > DateTime.now().year) {
      setState(() {
        _ageDetails = null;
      });
      return;
    }

    // Create date of birth
    DateTime dob;
    try {
      dob = DateTime(year, month, day);
    } catch (e) {
      setState(() {
        _ageDetails = null;
      });
      return;
    }

    final now = DateTime.now();

    // Basic validation
    if (dob.isAfter(now)) {
      setState(() {
        _ageDetails = null;
      });
      return;
    }

    // Calculate years, months, and days
    int years = now.year - dob.year;
    int months = now.month - dob.month;
    int days = now.day - dob.day;

    // Adjust for negative months or days
    if (days < 0) {
      // Go back one month
      months--;
      // Add days in the previous month
      days += _daysInMonth(now.year, now.month - 1);
    }

    if (months < 0) {
      // Go back one year
      years--;
      // Add 12 months
      months += 12;
    }

    // Calculate total values
    final totalDays = now.difference(dob).inDays;
    final totalMonths = (years * 12) + months;
    final totalWeeks = totalDays ~/ 7;
    final totalHours = totalDays * 24;
    final totalMinutes = totalHours * 60;

    setState(() {
      _ageDetails = {
        'years': years,
        'months': months,
        'days': days,
        'totalMonths': totalMonths,
        'totalWeeks': totalWeeks,
        'totalDays': totalDays,
        'totalHours': totalHours,
        'totalMinutes': totalMinutes,
      };
    });
  }

  int _daysInMonth(int year, int month) {
    // Adjust month if it's out of range
    if (month <= 0) {
      year--;
      month += 12;
    }
    
    // Handle leap years for February
    if (month == 2) {
      return (year % 4 == 0 && (year % 100 != 0 || year % 400 == 0)) ? 29 : 28;
    }
    
    // Return days for other months
    const daysInMonth = [0, 31, 28, 31, 30, 31, 30, 31, 31, 30, 31, 30, 31];
    return daysInMonth[month];
  }

  @override
  Widget build(BuildContext context) {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;

    return Scaffold(
      appBar: AppBar(
        toolbarHeight: kToolbarHeight + 24, // Add 3 units (24px) of height
        backgroundColor: Colors.blue.shade700,
        foregroundColor: Colors.white,
        automaticallyImplyLeading: false, // Remove back arrow
        flexibleSpace: Container(
          padding: const EdgeInsets.only(top: 24), // Lower AppBar by 3 units
          alignment: Alignment.center,
          child: Text(
            'Age Calculator',
            style: GoogleFonts.poppins(
              fontWeight: FontWeight.bold,
              fontSize: 20,
              color: Colors.white,
            ),
          ),
        ),
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Input Section
            Card(
              elevation: 4,
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Enter Date of Birth',
                      style: GoogleFonts.poppins(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                        color: Colors.blue.shade700,
                      ),
                    ),
                    const SizedBox(height: 16),
                    Row(
                      children: [
                        Expanded(
                          child: TextField(
                            controller: _dayController,
                            keyboardType: TextInputType.number,
                            inputFormatters: [
                              FilteringTextInputFormatter.digitsOnly,
                              LengthLimitingTextInputFormatter(2),
                            ],
                            decoration: const InputDecoration(
                              labelText: 'Day',
                              hintText: 'DD',
                              border: OutlineInputBorder(),
                            ),
                            onChanged: (value) => _calculateAge(),
                          ),
                        ),
                        const SizedBox(width: 12),
                        Expanded(
                          child: TextField(
                            controller: _monthController,
                            keyboardType: TextInputType.number,
                            inputFormatters: [
                              FilteringTextInputFormatter.digitsOnly,
                              LengthLimitingTextInputFormatter(2),
                            ],
                            decoration: const InputDecoration(
                              labelText: 'Month',
                              hintText: 'MM',
                              border: OutlineInputBorder(),
                            ),
                            onChanged: (value) => _calculateAge(),
                          ),
                        ),
                        const SizedBox(width: 12),
                        Expanded(
                          child: TextField(
                            controller: _yearController,
                            keyboardType: TextInputType.number,
                            inputFormatters: [
                              FilteringTextInputFormatter.digitsOnly,
                              LengthLimitingTextInputFormatter(4),
                            ],
                            decoration: const InputDecoration(
                              labelText: 'Year',
                              hintText: 'YYYY',
                              border: OutlineInputBorder(),
                            ),
                            onChanged: (value) => _calculateAge(),
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 16),
                    SizedBox(
                      width: double.infinity,
                      child: ElevatedButton(
                        onPressed: _calculateAge,
                        style: ElevatedButton.styleFrom(
                          backgroundColor: Colors.blue.shade700,
                          foregroundColor: Colors.white,
                          padding: const EdgeInsets.symmetric(vertical: 12),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(8),
                          ),
                        ),
                        child: Text(
                          'Calculate Age',
                          style: GoogleFonts.poppins(
                            fontSize: 16,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ),

            const SizedBox(height: 24),

            // Results Section
            if (_ageDetails != null)
              Card(
                elevation: 4,
                child: Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        children: [
                          Icon(Icons.cake, color: Colors.blue.shade700),
                          const SizedBox(width: 8),
                          Text(
                            'Age Details',
                            style: GoogleFonts.poppins(
                              fontSize: 18,
                              fontWeight: FontWeight.bold,
                              color: Colors.blue.shade700,
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(height: 16),
                      // Main exact age result
                      Container(
                        width: double.infinity,
                        padding: const EdgeInsets.all(16),
                        decoration: BoxDecoration(
                          color: Colors.blue.shade50,
                          borderRadius: BorderRadius.circular(12),
                          border: Border.all(color: Colors.blue.shade200),
                        ),
                        child: Column(
                          children: [
                            Text(
                              'Exact Age',
                              style: GoogleFonts.poppins(
                                fontSize: 16,
                                fontWeight: FontWeight.w600,
                                color: Colors.blue.shade700,
                              ),
                            ),
                            const SizedBox(height: 8),
                            Text(
                              '${_ageDetails!['years']} Years, ${_ageDetails!['months']} Months, ${_ageDetails!['days']} Days',
                              style: GoogleFonts.poppins(
                                fontSize: 20,
                                fontWeight: FontWeight.bold,
                                color: Colors.blue.shade800,
                              ),
                              textAlign: TextAlign.center,
                            ),
                          ],
                        ),
                      ),
                      const SizedBox(height: 16),
                      Text(
                        'Age Breakdown',
                        style: GoogleFonts.poppins(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                          color: Colors.blue.shade700,
                        ),
                      ),
                      const SizedBox(height: 12),
                      _buildResultItem(
                        'Total Years',
                        '${_ageDetails!['years']}',
                        isDarkMode,
                      ),
                      _buildResultItem(
                        'Total Months',
                        '${_ageDetails!['totalMonths']}',
                        isDarkMode,
                      ),
                      _buildResultItem(
                        'Total Weeks',
                        '${_ageDetails!['totalWeeks']}',
                        isDarkMode,
                      ),
                      _buildResultItem(
                        'Total Days',
                        '${_ageDetails!['totalDays']}',
                        isDarkMode,
                      ),
                      _buildResultItem(
                        'Total Hours',
                        '${_ageDetails!['totalHours']}',
                        isDarkMode,
                      ),
                      _buildResultItem(
                        'Total Minutes',
                        '${_ageDetails!['totalMinutes']}',
                        isDarkMode,
                      ),
                    ],
                  ),
                ),
              ),

            const SizedBox(height: 24),

            // Info Card
            Card(
              elevation: 2,
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        Icon(Icons.info_outline, color: Colors.blue.shade700),
                        const SizedBox(width: 8),
                        Text(
                          'Information',
                          style: GoogleFonts.poppins(
                            fontSize: 16,
                            fontWeight: FontWeight.bold,
                            color: Colors.blue.shade700,
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 12),
                    Text(
                      '• Enter your date of birth using the Day, Month, and Year fields\n'
                      '• Age is calculated automatically as you type\n'
                      '• Results show your exact age and breakdown in various time units',
                      style: GoogleFonts.poppins(
                        fontSize: 14,
                        color: Colors.grey.shade700,
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildResultItem(String label, String value, bool isDarkMode) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 12.0),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            label,
            style: GoogleFonts.poppins(
              fontSize: 14,
              fontWeight: FontWeight.w500,
              color: isDarkMode ? Colors.grey.shade300 : Colors.grey.shade800,
            ),
          ),
          Text(
            value,
            style: GoogleFonts.poppins(
              fontSize: 16,
              fontWeight: FontWeight.bold,
              color: isDarkMode ? Colors.white : Colors.black87,
            ),
          ),
        ],
      ),
    );
  }
}