import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import '../models/textbook.dart';
import 'pdf_viewer_screen.dart';

class MediumBooksScreen extends StatelessWidget {
  final String className;
  final TextbookMedium medium;

  const MediumBooksScreen({
    super.key,
    required this.className,
    required this.medium,
  });

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(
          '$className - ${medium.mediumName}',
          style: GoogleFonts.poppins(
            fontSize: 20, // Larger font
            fontWeight: FontWeight.bold,
          ),
        ),
        toolbarHeight: 70, // Taller app bar
        elevation: 0,
      ),
      body: Container(
        decoration: BoxDecoration(
          // Wooden texture effect
          color:
              Theme.of(context).brightness == Brightness.dark
                  ? const Color(0xFF1E1E1E) // Dark mode background
                  : const Color(0xFFF5F5DC), // Light beige for light mode
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [
              Theme.of(context).brightness == Brightness.dark
                  ? const Color(0xFF2A2A2A)
                  : const Color(0xFFE6E6C8),
              Theme.of(context).brightness == Brightness.dark
                  ? const Color(0xFF1E1E1E)
                  : const Color(0xFFF5F5DC),
            ],
          ),
        ),
        child: ListView.builder(
          padding: const EdgeInsets.all(16.0),
          itemCount: medium.textbooks.length,
          itemBuilder: (context, index) {
            final textbook = medium.textbooks[index];
            return _buildTextbookCard(context, textbook);
          },
        ),
      ),
    );
  }

  Widget _buildTextbookCard(BuildContext context, Textbook textbook) {
    final Color cardColor = _getClassColor(className, context);

    return Card(
      margin: const EdgeInsets.only(bottom: 24.0),
      elevation: 5,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      child: InkWell(
        borderRadius: BorderRadius.circular(16),
        onTap: () {
          Navigator.push(
            context,
            MaterialPageRoute(
              builder:
                  (context) => PdfViewerScreen(
                    title: textbook.title,
                    pdfUrl: textbook.pdfUrl,
                  ),
            ),
          );
        },
        child: Column(
          children: [
            // Header
            Container(
              width: double.infinity,
              padding: const EdgeInsets.symmetric(
                vertical: 20.0,
                horizontal: 20.0,
              ),
              decoration: BoxDecoration(
                color: cardColor.withAlpha(230), // More vibrant color
                borderRadius: const BorderRadius.only(
                  topLeft: Radius.circular(16),
                  topRight: Radius.circular(16),
                ),
                boxShadow: [
                  BoxShadow(
                    color: cardColor.withAlpha(60),
                    blurRadius: 6,
                    offset: const Offset(0, 2),
                  ),
                ],
              ),
              child: Row(
                children: [
                  Icon(
                    _getSubjectIcon(textbook.title),
                    color: Colors.white,
                    size: 36, // Larger icon
                  ),
                  const SizedBox(width: 16),
                  Expanded(
                    child: Text(
                      textbook.title,
                      style: GoogleFonts.poppins(
                        fontSize: 22, // Larger font
                        fontWeight: FontWeight.bold,
                        color: Colors.white,
                      ),
                    ),
                  ),
                ],
              ),
            ),

            // Description
            if (textbook.description != null)
              Container(
                width: double.infinity,
                padding: const EdgeInsets.all(24.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      textbook.description!,
                      style: GoogleFonts.poppins(
                        fontSize: 18, // Larger font
                        height: 1.5,
                        color:
                            Theme.of(context).brightness == Brightness.dark
                                ? Colors.white70
                                : Colors.black87,
                      ),
                    ),
                    const SizedBox(height: 20),
                    Row(
                      mainAxisAlignment: MainAxisAlignment.end,
                      children: [
                        SizedBox(
                          height: 50, // Taller button
                          child: ElevatedButton.icon(
                            icon: const Icon(
                              Icons.picture_as_pdf,
                              size: 24,
                            ), // Larger icon
                            label: Text(
                              'Open PDF',
                              style: GoogleFonts.poppins(
                                fontSize: 18, // Larger font
                                fontWeight: FontWeight.w600,
                              ),
                            ),
                            style: ElevatedButton.styleFrom(
                              backgroundColor: cardColor,
                              foregroundColor: Colors.white,
                              padding: const EdgeInsets.symmetric(
                                horizontal: 24,
                                vertical: 12,
                              ),
                              shape: RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(10),
                              ),
                              elevation: 4,
                            ),
                            onPressed: () {
                              Navigator.push(
                                context,
                                MaterialPageRoute(
                                  builder:
                                      (context) => PdfViewerScreen(
                                        title: textbook.title,
                                        pdfUrl: textbook.pdfUrl,
                                      ),
                                ),
                              );
                            },
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
          ],
        ),
      ),
    );
  }

  Color _getClassColor(String className, BuildContext context) {
    switch (className) {
      case 'Class 10':
        return const Color(0xFF7B1FA2); // Purple
      case 'Class 11':
        return const Color(0xFFEF6C00); // Orange
      case 'Class 12':
        return const Color(0xFFC62828); // Red
      default:
        return Theme.of(context).colorScheme.primary;
    }
  }

  IconData _getSubjectIcon(String subject) {
    final String lowerSubject = subject.toLowerCase();

    if (lowerSubject.contains('english')) {
      return Icons.language;
    } else if (lowerSubject.contains('math')) {
      return Icons.calculate;
    } else if (lowerSubject.contains('science')) {
      return Icons.science;
    } else if (lowerSubject.contains('physics')) {
      return Icons.bolt;
    } else if (lowerSubject.contains('chemistry')) {
      return Icons.science;
    } else if (lowerSubject.contains('biology')) {
      return Icons.biotech;
    } else if (lowerSubject.contains('social')) {
      return Icons.public;
    } else if (lowerSubject.contains('computer')) {
      return Icons.computer;
    } else if (lowerSubject.contains('nepali')) {
      return Icons.menu_book;
    } else {
      return Icons.book;
    }
  }
}
