import 'package:flutter/material.dart';
import 'package:flutter_custom_tabs/flutter_custom_tabs.dart';
import 'package:url_launcher/url_launcher.dart' as url_launcher;

// Import for navigator key and webview screen
import '../main.dart';
import '../legal_webview_screen.dart';

class ChromeCustomTabsService {
  static const Color _primaryColor = Color(0xFF1976D2); // App's blue color

  /// Launch URL in Chrome Custom Tabs with app's blue theme
  static Future<void> launch(String url, {String? title}) async {
    try {
      await launchUrl(
        Uri.parse(url),
        customTabsOptions: CustomTabsOptions(
          colorSchemes: CustomTabsColorSchemes.defaults(
            toolbarColor: _primaryColor,
          ),
          shareState: CustomTabsShareState.on,
          urlBarHidingEnabled: true,
          showTitle: true,
          closeButton: CustomTabsCloseButton(
            icon: CustomTabsCloseButtonIcons.back,
          ),
          animations: CustomTabsSystemAnimations.slideIn(),
          // Remove Chrome packages from fallbackCustomTabs as they're causing errors
          // The library will automatically use available browsers
          browser: const CustomTabsBrowserConfiguration(),
        ),
        safariVCOptions: SafariViewControllerOptions(
          preferredBarTintColor: _primaryColor,
          preferredControlTintColor: Colors.white,
          barCollapsingEnabled: true,
          entersReaderIfAvailable: false,
          dismissButtonStyle: SafariViewControllerDismissButtonStyle.close,
        ),
      );
    } catch (e) {
      // Fallback to default browser if Chrome Custom Tabs fails
      debugPrint('Chrome Custom Tabs failed: $e');
      await _launchFallback(url, title: title);
    }
  }

  /// Launch SEE Results with optimized settings
  static Future<void> launchSEEResults() async {
    await launch(
      'http://see.gov.np/exam/results',
      title: 'SEE Results',
    );
  }

  /// Launch HSEB/NEB Results with optimized settings
  static Future<void> launchHSEBResults() async {
    await launch(
      'https://www.neb.gov.np/results',
      title: 'HSEB/NEB Results',
    );
  }

  /// Launch TU Results with optimized settings
  static Future<void> launchTUResults() async {
    await launch(
      'https://result.tuexam.edu.np/',
      title: 'TU Results',
    );
  }

  /// Launch IPO Results with optimized settings
  static Future<void> launchIPOResults() async {
    await launch(
      'https://iporesult.cdsc.com.np/',
      title: 'IPO Results',
    );
  }

  /// Launch Mero Share with optimized settings
  static Future<void> launchMeroShare() async {
    await launch(
      'https://meroshare.cdsc.com.np/',
      title: 'Mero Share',
    );
  }

  /// Launch KU Results with optimized settings
  static Future<void> launchKUResults() async {
    await launch(
      'https://ku.edu.np/result',
      title: 'KU Results',
    );
  }

  /// Launch PU Results with optimized settings
  static Future<void> launchPUResults() async {
    await launch(
      'https://pu.edu.np/results',
      title: 'PU Results',
    );
  }

  /// Launch CTEVT Results with optimized settings
  static Future<void> launchCTEVTResults() async {
    await launch(
      'https://ctevt.org.np',
      title: 'CTEVT Results',
    );
  }

  /// Launch DV Lottery with optimized settings
  static Future<void> launchDVLottery() async {
    await launch(
      'https://dvprogram.state.gov',
      title: 'DV Lottery',
    );
  }

  /// Launch TU EduSanjal with optimized settings
  static Future<void> launchTUEduSanjal() async {
    await launch(
      'https://tu.edusanjal.com/',
      title: 'TU EduSanjal',
    );
  }

  /// Launch TU Official Website with optimized settings
  static Future<void> launchTUOfficial() async {
    await launch(
      'https://tribhuvan-university.edu.np',
      title: 'TU Official Website',
    );
  }



  /// Launch textbook PDFs with optimized settings for reading
  static Future<void> launchTextbookPDF(String url, String title) async {
    try {
      await launchUrl(
        Uri.parse(url),
        customTabsOptions: CustomTabsOptions(
          colorSchemes: CustomTabsColorSchemes.defaults(
            toolbarColor: _primaryColor,
          ),
          shareState: CustomTabsShareState.on,
          urlBarHidingEnabled: false, // Keep URL bar for PDFs
          showTitle: true,
          closeButton: CustomTabsCloseButton(
            icon: CustomTabsCloseButtonIcons.back,
          ),
          animations: CustomTabsSystemAnimations.slideIn(),
          // Remove Chrome packages from fallbackCustomTabs as they're causing errors
          // The library will automatically use available browsers
          browser: const CustomTabsBrowserConfiguration(),
        ),
        safariVCOptions: SafariViewControllerOptions(
          preferredBarTintColor: _primaryColor,
          preferredControlTintColor: Colors.white,
          barCollapsingEnabled: false, // Keep toolbar visible for PDFs
          entersReaderIfAvailable: true, // Enable reader mode for PDFs
          dismissButtonStyle: SafariViewControllerDismissButtonStyle.close,
        ),
      );
    } catch (e) {
      debugPrint('Chrome Custom Tabs failed for PDF: $e');
      await _launchFallback(url, title: title);
    }
  }

  /// Launch eKantipur results with news-optimized settings
  static Future<void> launchEKantipurResults(String url, String title) async {
    try {
      await launchUrl(
        Uri.parse(url),
        customTabsOptions: CustomTabsOptions(
          colorSchemes: CustomTabsColorSchemes.defaults(
            toolbarColor: Colors.red.shade700, // eKantipur brand color
          ),
          shareState: CustomTabsShareState.on,
          urlBarHidingEnabled: true,
          showTitle: true,
          closeButton: CustomTabsCloseButton(
            icon: CustomTabsCloseButtonIcons.back,
          ),
          animations: CustomTabsSystemAnimations.slideIn(),
        ),
        safariVCOptions: SafariViewControllerOptions(
          preferredBarTintColor: Colors.red.shade700,
          preferredControlTintColor: Colors.white,
          barCollapsingEnabled: true,
          entersReaderIfAvailable: true,
          dismissButtonStyle: SafariViewControllerDismissButtonStyle.close,
        ),
      );
    } catch (e) {
      debugPrint('Chrome Custom Tabs failed for eKantipur: $e');
      await _launchFallback(url, title: title);
    }
  }

  /// Launch NTC portal with telecom-optimized settings
  static Future<void> launchNTCPortal(String url, String title) async {
    try {
      await launchUrl(
        Uri.parse(url),
        customTabsOptions: CustomTabsOptions(
          colorSchemes: CustomTabsColorSchemes.defaults(
            toolbarColor: Colors.blue.shade800, // NTC brand color
          ),
          shareState: CustomTabsShareState.on,
          urlBarHidingEnabled: true,
          showTitle: true,
          closeButton: CustomTabsCloseButton(
            icon: CustomTabsCloseButtonIcons.back,
          ),
          animations: CustomTabsSystemAnimations.slideIn(),
        ),
        safariVCOptions: SafariViewControllerOptions(
          preferredBarTintColor: Colors.blue.shade800,
          preferredControlTintColor: Colors.white,
          barCollapsingEnabled: true,
          entersReaderIfAvailable: false,
          dismissButtonStyle: SafariViewControllerDismissButtonStyle.close,
        ),
      );
    } catch (e) {
      debugPrint('Chrome Custom Tabs failed for NTC: $e');
      await _launchFallback(url, title: title);
    }
  }

  /// Fallback to in-app WebView if Chrome Custom Tabs fails
  static Future<void> _launchFallback(String url, {String? title}) async {
    try {
      await Navigator.of(navigatorKey.currentContext!).push(
        MaterialPageRoute(
          builder: (context) => LegalWebViewScreen(url: url, title: title ?? 'Web View'),
        ),
      );
    } catch (e) {
      debugPrint('WebView fallback failed: $e');
      if (await url_launcher.canLaunchUrl(Uri.parse(url))) {
        await url_launcher.launchUrl(
          Uri.parse(url),
          mode: url_launcher.LaunchMode.inAppWebView,
        );
      }
    }
  }

  /// Show loading dialog while launching
  static Future<void> launchWithLoading(
    BuildContext context,
    String url, {
    String? title,
    String loadingMessage = 'Opening...',
  }) async {
    // Show loading dialog
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return AlertDialog(
          content: Row(
            children: [
              const CircularProgressIndicator(),
              const SizedBox(width: 20),
              Text(loadingMessage),
            ],
          ),
        );
      },
    );

    try {
      await launch(url, title: title);
    } finally {
      // Close loading dialog
      if (context.mounted) {
        Navigator.of(context).pop();
      }
    }
  }

  /// Check if Chrome Custom Tabs is available
  static Future<bool> isAvailable() async {
    try {
      // Try to launch a test URL to check availability
      return true; // flutter_custom_tabs handles availability internally
    } catch (e) {
      return false;
    }
  }
}