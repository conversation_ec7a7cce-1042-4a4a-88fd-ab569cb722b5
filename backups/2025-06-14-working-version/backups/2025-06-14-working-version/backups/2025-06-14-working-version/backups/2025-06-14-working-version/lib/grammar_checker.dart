import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:clipboard/clipboard.dart';
import '../../utils/romanized_nepali_converter.dart';

class <PERSON><PERSON><PERSON><PERSON> extends StatefulWidget {
  const GrammarChecker({super.key});

  @override
  State<GrammarChecker> createState() => _GrammarCheckerState();
}

class _GrammarCheckerState extends State<GrammarChecker> {
  // Text controllers
  final TextEditingController _textController = TextEditingController();

  // Focus node
  final FocusNode _focusNode = FocusNode();

  // Grammar check results
  String _correctedText = '';
  List<Map<String, dynamic>> _grammarIssues = [];

  // Loading state
  bool _isChecking = false;

  // Error message
  String _errorMessage = '';

  // Input type detection
  String _detectedInputType =
      'unknown'; // 'unknown', 'romanized', or 'devanagari'

  @override
  void initState() {
    super.initState();

    // Listen for text changes to detect input type
    _textController.addListener(_detectInputType);
  }

  @override
  void dispose() {
    _textController.removeListener(_detectInputType);
    _textController.dispose();
    _focusNode.dispose();
    super.dispose();
  }

  // Detect input type
  void _detectInputType() {
    if (_textController.text.isNotEmpty) {
      setState(() {
        _detectedInputType = RomanizedNepaliConverter.detectScriptType(
          _textController.text,
        );
      });
    } else {
      setState(() {
        _detectedInputType = 'unknown';
      });
    }
  }

  // Check grammar
  Future<void> _checkGrammar() async {
    final text = _textController.text.trim();

    if (text.isEmpty) {
      setState(() {
        _errorMessage = 'Please enter text to check';
        _correctedText = '';
        _grammarIssues = [];
      });
      return;
    }

    // Check if the text is in Devanagari script
    if (_detectedInputType != 'devanagari') {
      // Convert Romanized Nepali to Devanagari if needed
      if (_detectedInputType == 'romanized') {
        final convertedText = RomanizedNepaliConverter.convertToDevanagari(
          text,
        );
        setState(() {
          _textController.text = convertedText;
        });

        // Show a message about the conversion
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Converted Romanized Nepali to Devanagari script'),
            duration: const Duration(seconds: 2),
          ),
        );
      } else {
        setState(() {
          _errorMessage =
              'Please enter text in Nepali script (Devanagari) for grammar checking';
          _correctedText = '';
          _grammarIssues = [];
        });
        return;
      }
    }

    setState(() {
      _isChecking = true;
      _errorMessage = '';
      _correctedText = '';
      _grammarIssues = [];
    });

    try {
      // Simulate grammar checking (in a real app, this would call an API)
      await Future.delayed(const Duration(seconds: 2));

      // Mock grammar issues and corrections
      final issues = _mockGrammarCheck(text);

      setState(() {
        _grammarIssues = issues;
        _correctedText = _applyCorrections(text, issues);
        _isChecking = false;
      });
    } catch (e) {
      setState(() {
        _errorMessage = 'Grammar check failed: $e';
        _isChecking = false;
      });
    }
  }

  // Mock grammar check function
  List<Map<String, dynamic>> _mockGrammarCheck(String text) {
    // This is a simplified mock function
    // In a real app, this would be replaced with an actual grammar checking API

    final List<Map<String, dynamic>> issues = [];

    // Common Nepali grammar issues to check for
    final patterns = [
      {
        'pattern': 'मेरो नाम हो',
        'suggestion': 'मेरो नाम हो।',
        'type': 'punctuation',
        'message': 'वाक्यको अन्त्यमा पूर्णविराम चिन्ह (।) थप्नुहोस्।',
      },
      {
        'pattern': 'म घर जान्छु',
        'suggestion': 'म घर जान्छु।',
        'type': 'punctuation',
        'message': 'वाक्यको अन्त्यमा पूर्णविराम चिन्ह (।) थप्नुहोस्।',
      },
      {
        'pattern': 'तिमी कहा जान्छौ',
        'suggestion': 'तिमी कहाँ जान्छौ?',
        'type': 'spelling',
        'message':
            '"कहा" को सट्टा "कहाँ" प्रयोग गर्नुहोस् र प्रश्नवाचक चिन्ह (?) थप्नुहोस्।',
      },
      {
        'pattern': 'म स्कुल जान्छु',
        'suggestion': 'म स्कूल जान्छु।',
        'type': 'spelling',
        'message': '"स्कुल" को सट्टा "स्कूल" प्रयोग गर्नुहोस्।',
      },
      {
        'pattern': 'राम्रो छ',
        'suggestion': 'राम्रो छ।',
        'type': 'punctuation',
        'message': 'वाक्यको अन्त्यमा पूर्णविराम चिन्ह (।) थप्नुहोस्।',
      },
    ];

    // Check for patterns in the text
    for (final pattern in patterns) {
      if (text.contains(pattern['pattern'] as String)) {
        issues.add({
          'original': pattern['pattern'],
          'suggestion': pattern['suggestion'],
          'type': pattern['type'],
          'message': pattern['message'],
        });
      }
    }

    return issues;
  }

  // Apply corrections to the text
  String _applyCorrections(
    String originalText,
    List<Map<String, dynamic>> issues,
  ) {
    String correctedText = originalText;

    for (final issue in issues) {
      correctedText = correctedText.replaceAll(
        issue['original'],
        issue['suggestion'],
      );
    }

    return correctedText;
  }

  // Copy corrected text to clipboard
  void _copyToClipboard() {
    if (_correctedText.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('No corrected text to copy'),
          duration: Duration(seconds: 2),
        ),
      );
      return;
    }

    FlutterClipboard.copy(_correctedText).then((_) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Corrected text copied to clipboard'),
            duration: Duration(seconds: 2),
          ),
        );
      }
    });
  }

  // Clear text
  void _clearText() {
    setState(() {
      _textController.clear();
      _correctedText = '';
      _grammarIssues = [];
      _errorMessage = '';
      _detectedInputType = 'unknown';
    });
  }

  // Accept all suggestions
  void _acceptAllSuggestions() {
    if (_correctedText.isEmpty) {
      return;
    }

    setState(() {
      _textController.text = _correctedText;
    });

    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('All suggestions applied'),
        duration: Duration(seconds: 2),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;

    return Scaffold(
      appBar: AppBar(
        title: Text(
          'Grammar Checker',
          style: GoogleFonts.poppins(fontWeight: FontWeight.bold),
        ),
        elevation: 0,
      ),
      body: SafeArea(
        child: SingleChildScrollView(
          physics: const BouncingScrollPhysics(),
          child: Padding(
            padding: const EdgeInsets.all(16.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Input instruction
                Container(
                  width: double.infinity,
                  padding: const EdgeInsets.all(12),
                  margin: const EdgeInsets.only(bottom: 16),
                  decoration: BoxDecoration(
                    color:
                        isDarkMode
                            ? Colors.blue.withAlpha(30)
                            : Colors.blue.withAlpha(15),
                    borderRadius: BorderRadius.circular(12),
                    border: Border.all(
                      color: Colors.blue.withAlpha(50),
                      width: 1,
                    ),
                  ),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        children: [
                          Icon(Icons.spellcheck, size: 16, color: Colors.blue),
                          const SizedBox(width: 8),
                          Expanded(
                            child: Text(
                              'Nepali Grammar Checker',
                              style: GoogleFonts.poppins(
                                fontSize: 14,
                                fontWeight: FontWeight.bold,
                                color: Colors.blue,
                              ),
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(height: 8),
                      Text(
                        'Type or paste Nepali text to check for grammar mistakes.',
                        style: GoogleFonts.poppins(fontSize: 13),
                      ),
                    ],
                  ),
                ),

                // Input field with script type indicator
                Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    TextField(
                      controller: _textController,
                      focusNode: _focusNode,
                      decoration: InputDecoration(
                        labelText: 'Enter Nepali text',
                        hintText: 'यहाँ नेपाली भाषामा टाइप गर्नुहोस्...',
                        helperText: 'Type in Nepali script for best results',
                        helperStyle: GoogleFonts.poppins(
                          fontSize: 12,
                          color: Colors.blue,
                        ),
                        border: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(12),
                        ),
                        prefixIcon: Icon(Icons.edit, color: Colors.blue),
                        suffixIcon: Row(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            // Input type indicator
                            if (_detectedInputType != 'unknown')
                              Padding(
                                padding: const EdgeInsets.only(right: 8.0),
                                child: Container(
                                  padding: const EdgeInsets.symmetric(
                                    horizontal: 8,
                                    vertical: 4,
                                  ),
                                  decoration: BoxDecoration(
                                    color:
                                        _detectedInputType == 'romanized'
                                            ? Colors.orange.withAlpha(50)
                                            : Colors.green.withAlpha(50),
                                    borderRadius: BorderRadius.circular(12),
                                    border: Border.all(
                                      color:
                                          _detectedInputType == 'romanized'
                                              ? Colors.orange
                                              : Colors.green,
                                      width: 1,
                                    ),
                                  ),
                                  child: Text(
                                    _detectedInputType == 'romanized'
                                        ? 'Romanized'
                                        : 'Devanagari',
                                    style: GoogleFonts.poppins(
                                      fontSize: 10,
                                      color:
                                          _detectedInputType == 'romanized'
                                              ? Colors.orange.shade800
                                              : Colors.green.shade800,
                                      fontWeight: FontWeight.bold,
                                    ),
                                  ),
                                ),
                              ),
                            // Clear button
                            IconButton(
                              icon: const Icon(Icons.clear),
                              onPressed: _clearText,
                            ),
                          ],
                        ),
                      ),
                      maxLines: 5,
                    ),
                  ],
                ),

                if (_errorMessage.isNotEmpty)
                  Padding(
                    padding: const EdgeInsets.only(top: 8.0),
                    child: Text(
                      _errorMessage,
                      style: GoogleFonts.poppins(
                        color: Colors.red,
                        fontSize: 12,
                      ),
                    ),
                  ),

                const SizedBox(height: 16),

                // Check grammar button
                SizedBox(
                  width: double.infinity,
                  child: ElevatedButton(
                    onPressed: _isChecking ? null : _checkGrammar,
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.blue,
                      foregroundColor: Colors.white,
                      padding: const EdgeInsets.symmetric(vertical: 12),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(12),
                      ),
                    ),
                    child:
                        _isChecking
                            ? const SizedBox(
                              height: 20,
                              width: 20,
                              child: CircularProgressIndicator(
                                strokeWidth: 2,
                                color: Colors.white,
                              ),
                            )
                            : Text(
                              'Check Grammar',
                              style: GoogleFonts.poppins(
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                  ),
                ),

                const SizedBox(height: 24),

                // Grammar issues
                if (_grammarIssues.isNotEmpty) ...[
                  Text(
                    'Grammar Issues:',
                    style: GoogleFonts.poppins(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                      color: Colors.blue,
                    ),
                  ),
                  const SizedBox(height: 8),
                  ListView.builder(
                    shrinkWrap: true,
                    physics: const NeverScrollableScrollPhysics(),
                    itemCount: _grammarIssues.length,
                    itemBuilder: (context, index) {
                      final issue = _grammarIssues[index];
                      return Card(
                        margin: const EdgeInsets.only(bottom: 8),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(12),
                          side: BorderSide(
                            color: _getIssueColor(issue['type']).withAlpha(100),
                            width: 1,
                          ),
                        ),
                        child: Padding(
                          padding: const EdgeInsets.all(12),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Row(
                                children: [
                                  Icon(
                                    _getIssueIcon(issue['type']),
                                    size: 16,
                                    color: _getIssueColor(issue['type']),
                                  ),
                                  const SizedBox(width: 8),
                                  Text(
                                    _getIssueTitle(issue['type']),
                                    style: GoogleFonts.poppins(
                                      fontSize: 14,
                                      fontWeight: FontWeight.bold,
                                      color: _getIssueColor(issue['type']),
                                    ),
                                  ),
                                ],
                              ),
                              const SizedBox(height: 8),
                              Text(
                                issue['message'],
                                style: GoogleFonts.poppins(fontSize: 13),
                              ),
                              const SizedBox(height: 8),
                              Row(
                                children: [
                                  Expanded(
                                    child: Container(
                                      padding: const EdgeInsets.all(8),
                                      decoration: BoxDecoration(
                                        color: Colors.red.withAlpha(20),
                                        borderRadius: BorderRadius.circular(8),
                                      ),
                                      child: Text(
                                        issue['original'],
                                        style: GoogleFonts.poppins(
                                          fontSize: 13,
                                          color: Colors.red,
                                        ),
                                      ),
                                    ),
                                  ),
                                  const SizedBox(width: 8),
                                  const Icon(
                                    Icons.arrow_forward,
                                    size: 16,
                                    color: Colors.grey,
                                  ),
                                  const SizedBox(width: 8),
                                  Expanded(
                                    child: Container(
                                      padding: const EdgeInsets.all(8),
                                      decoration: BoxDecoration(
                                        color: Colors.green.withAlpha(20),
                                        borderRadius: BorderRadius.circular(8),
                                      ),
                                      child: Text(
                                        issue['suggestion'],
                                        style: GoogleFonts.poppins(
                                          fontSize: 13,
                                          color: Colors.green,
                                        ),
                                      ),
                                    ),
                                  ),
                                ],
                              ),
                            ],
                          ),
                        ),
                      );
                    },
                  ),
                ],

                const SizedBox(height: 16),

                // Corrected text
                if (_correctedText.isNotEmpty) ...[
                  Text(
                    'Corrected Text:',
                    style: GoogleFonts.poppins(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                      color: Colors.blue,
                    ),
                  ),
                  const SizedBox(height: 8),
                  Container(
                    width: double.infinity,
                    padding: const EdgeInsets.all(16),
                    decoration: BoxDecoration(
                      color:
                          isDarkMode
                              ? Colors.blue.withAlpha(30)
                              : Colors.blue.withAlpha(15),
                      borderRadius: BorderRadius.circular(12),
                      border: Border.all(
                        color: Colors.blue.withAlpha(50),
                        width: 1,
                      ),
                    ),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          _correctedText,
                          style: GoogleFonts.poppins(
                            fontSize: 16,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                        const SizedBox(height: 16),
                        Row(
                          mainAxisAlignment: MainAxisAlignment.end,
                          children: [
                            // Apply all suggestions button
                            OutlinedButton.icon(
                              onPressed: _acceptAllSuggestions,
                              icon: const Icon(Icons.check_circle_outline),
                              label: Text(
                                'Apply All',
                                style: GoogleFonts.poppins(
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                              style: OutlinedButton.styleFrom(
                                foregroundColor: Colors.green,
                                side: const BorderSide(color: Colors.green),
                                shape: RoundedRectangleBorder(
                                  borderRadius: BorderRadius.circular(12),
                                ),
                              ),
                            ),
                            const SizedBox(width: 8),
                            // Copy button
                            OutlinedButton.icon(
                              onPressed: _copyToClipboard,
                              icon: const Icon(Icons.copy),
                              label: Text(
                                'Copy',
                                style: GoogleFonts.poppins(
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                              style: OutlinedButton.styleFrom(
                                foregroundColor: Colors.blue,
                                side: const BorderSide(color: Colors.blue),
                                shape: RoundedRectangleBorder(
                                  borderRadius: BorderRadius.circular(12),
                                ),
                              ),
                            ),
                          ],
                        ),
                      ],
                    ),
                  ),
                ],
              ],
            ),
          ),
        ),
      ),
    );
  }

  // Helper methods for issue types
  Color _getIssueColor(String type) {
    switch (type) {
      case 'spelling':
        return Colors.orange;
      case 'grammar':
        return Colors.red;
      case 'punctuation':
        return Colors.purple;
      default:
        return Colors.blue;
    }
  }

  IconData _getIssueIcon(String type) {
    switch (type) {
      case 'spelling':
        return Icons.spellcheck;
      case 'grammar':
        return Icons.text_fields;
      case 'punctuation':
        return Icons.format_quote;
      default:
        return Icons.error_outline;
    }
  }

  String _getIssueTitle(String type) {
    switch (type) {
      case 'spelling':
        return 'Spelling Issue';
      case 'grammar':
        return 'Grammar Issue';
      case 'punctuation':
        return 'Punctuation Issue';
      default:
        return 'Issue';
    }
  }
}
