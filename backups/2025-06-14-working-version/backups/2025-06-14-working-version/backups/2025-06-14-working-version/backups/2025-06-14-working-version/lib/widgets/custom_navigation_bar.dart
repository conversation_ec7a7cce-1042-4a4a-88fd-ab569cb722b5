import 'package:flutter/material.dart';

class CustomNavigationBar extends StatelessWidget {
  final int currentIndex;
  final Function(int) onTap;

  const CustomNavigationBar({
    super.key,
    required this.currentIndex,
    required this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;
    final backgroundColor = isDarkMode ? Colors.grey[900] : Colors.white;
    final selectedGradient = LinearGradient(
      colors: const [Color(0xFF2196F3), Color(0xFF448AFF)],
      begin: Alignment.topLeft,
      end: Alignment.bottomRight,
    );
    
    return Container(
      decoration: BoxDecoration(
        color: backgroundColor,
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 10,
            offset: const Offset(0, -2),
          ),
        ],
      ),
      child: BottomNavigationBar(
        currentIndex: currentIndex,
        onTap: onTap,
        type: BottomNavigationBarType.fixed,
        backgroundColor: Colors.transparent,
        elevation: 0,
        selectedItemColor: Colors.blue,
        unselectedItemColor: isDarkMode ? Colors.grey[400] : Colors.grey[600],
        selectedLabelStyle: const TextStyle(fontWeight: FontWeight.bold),
        items: [
          BottomNavigationBarItem(
            icon: _buildIcon(Icons.home, 0 == currentIndex, context),
            label: 'Home',
          ),
          BottomNavigationBarItem(
            icon: _buildIcon(Icons.book, 1 == currentIndex, context),
            label: 'Books',
          ),
          BottomNavigationBarItem(
            icon: _buildIcon(Icons.calculate, 2 == currentIndex, context),
            label: 'Tools',
          ),
          BottomNavigationBarItem(
            icon: _buildIcon(Icons.note, 3 == currentIndex, context),
            label: 'Notes',
          ),
        ],
      ),
    );
  }

  Widget _buildIcon(IconData icon, bool isSelected, BuildContext context) {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;
    final selectedColor = const Color(0xFF448AFF);
    final unselectedColor = isDarkMode ? Colors.grey[400]! : Colors.grey[600]!;
    
    return AnimatedContainer(
      duration: const Duration(milliseconds: 300),
      height: 40,
      width: 40,
      decoration: BoxDecoration(
        color: isSelected 
          ? isDarkMode ? Colors.blue.withOpacity(0.2) : Colors.blue.withOpacity(0.1)
          : Colors.transparent,
        borderRadius: BorderRadius.circular(10),
      ),
      child: Stack(
        alignment: Alignment.center,
        children: [
          // Background glow effect for selected item
          if (isSelected)
            Container(
              height: 5,
              width: 25,
              margin: const EdgeInsets.only(top: 30),
              decoration: BoxDecoration(
                color: selectedColor.withOpacity(0.7),
                borderRadius: BorderRadius.circular(10),
                boxShadow: [
                  BoxShadow(
                    color: selectedColor.withOpacity(0.3),
                    blurRadius: 8,
                    spreadRadius: 1,
                  ),
                ],
              ),
            ),
          // Icon with animation
          AnimatedScale(
            scale: isSelected ? 1.2 : 1.0,
            duration: const Duration(milliseconds: 200),
            child: AnimatedContainer(
              duration: const Duration(milliseconds: 300),
              child: ShaderMask(
                blendMode: BlendMode.srcIn,
                shaderCallback: (Rect bounds) {
                  return LinearGradient(
                    colors: isSelected 
                      ? [const Color(0xFF2196F3), const Color(0xFF448AFF)]
                      : [unselectedColor, unselectedColor],
                    begin: Alignment.topLeft,
                    end: Alignment.bottomRight,
                  ).createShader(bounds);
                },
                child: Icon(icon),
              ),
            ),
          ),
        ],
      ),
    );
  }
}
