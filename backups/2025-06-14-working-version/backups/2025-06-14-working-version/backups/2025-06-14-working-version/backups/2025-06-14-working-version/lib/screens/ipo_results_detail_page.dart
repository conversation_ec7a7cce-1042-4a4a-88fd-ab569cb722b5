import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import '../services/chrome_custom_tabs_service.dart';

class IPOResultsDetailPage extends StatefulWidget {
  const IPOResultsDetailPage({super.key});

  @override
  State<IPOResultsDetailPage> createState() => _IPOResultsDetailPageState();
}

class _IPOResultsDetailPageState extends State<IPOResultsDetailPage> {
  Future<void> _launchChromeCustomTab(String url, String title) async {
    try {
      if (url.contains('iporesult.cdsc.com.np')) {
        await ChromeCustomTabsService.launchIPOResults();
      } else if (url.contains('meroshare.cdsc.com.np')) {
        await ChromeCustomTabsService.launchMeroShare();
      } else {
        await ChromeCustomTabsService.launch(url, title: title);
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Could not open $title: ${e.toString()}')),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('IPO Results'),
        backgroundColor: Colors.purple.shade700,
        foregroundColor: Colors.white,
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header with IPO logo
            Card(
              elevation: 4,
              child: Padding(
                padding: const EdgeInsets.all(20.0),
                child: Row(
                  children: [
                    Container(
                      width: 60,
                      height: 60,
                      decoration: BoxDecoration(
                        color: Colors.purple.shade100,
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: Icon(
                        Icons.trending_up,
                        size: 40,
                        color: Colors.purple.shade700,
                      ),
                    ),
                    const SizedBox(width: 16),
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            'IPO Results',
                            style: GoogleFonts.poppins(
                              fontSize: 20,
                              fontWeight: FontWeight.bold,
                              color: Colors.purple.shade700,
                            ),
                          ),
                          const SizedBox(height: 4),
                          Text(
                            'Initial Public Offering Results',
                            style: GoogleFonts.poppins(
                              fontSize: 14,
                              color: Colors.grey.shade600,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
            ),

            const SizedBox(height: 24),

            // Official Sites Section
            _buildSectionTitle('Official Sites', Icons.public),
            const SizedBox(height: 16),
            _buildWebsiteCard(
              'CDSC IPO Result',
              'https://iporesult.cdsc.com.np',
              Icons.trending_up,
              Colors.purple,
              () => ChromeCustomTabsService.launchIPOResults(),
            ),

            const SizedBox(height: 32),

            // Information Section
            _buildSectionTitle('Information', Icons.info_outline),
            const SizedBox(height: 16),
            Card(
              elevation: 2,
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'About IPO Results',
                      style: GoogleFonts.poppins(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                        color: Colors.purple.shade700,
                      ),
                    ),
                    const SizedBox(height: 12),
                    Text(
                      '• IPO results show share allotment for Initial Public Offerings\n'
                      '• Results are published on the official CDSC website\n'
                      '• Check your results using your BOID or application number\n'
                      '• Use Mero Share for online share trading and portfolio management\n'
                      '• Contact your broker for any queries regarding share allotment',
                      style: GoogleFonts.poppins(
                        fontSize: 14,
                        color: Colors.grey.shade700,
                      ),
                    ),
                  ],
                ),
              ),
            ),

            const SizedBox(height: 24),

            // Quick Links Section
            _buildSectionTitle('Quick Links', Icons.link),
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: _buildQuickLinkCard(
                    'NEPSE',
                    Icons.bar_chart,
                    Colors.green,
                    () => _launchChromeCustomTab('https://www.nepalstock.com/', 'NEPSE'),
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: _buildQuickLinkCard(
                    'Share Sansar',
                    Icons.newspaper,
                    Colors.orange,
                    () => _launchChromeCustomTab('https://www.sharesansar.com/', 'Share Sansar'),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSectionTitle(String title, IconData icon) {
    return Row(
      children: [
        Icon(icon, color: Colors.purple.shade700, size: 24),
        const SizedBox(width: 8),
        Text(
          title,
          style: GoogleFonts.poppins(
            fontSize: 18,
            fontWeight: FontWeight.bold,
            color: Colors.purple.shade700,
          ),
        ),
      ],
    );
  }

  Widget _buildWebsiteCard(String title, String url, IconData icon, Color color, [VoidCallback? customLaunch]) {
    return Card(
      elevation: 2,
      child: InkWell(
        onTap: () async {
          try {
            if (customLaunch != null) {
              customLaunch();
            } else {
              await _launchChromeCustomTab(url, title);
            }
          } catch (e) {
            if (mounted) {
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(content: Text('Could not open $title: ${e.toString()}')),
              );
            }
          }
        },
        borderRadius: BorderRadius.circular(12),
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Row(
            children: [
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: color.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Icon(icon, color: color, size: 24),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      title,
                      style: GoogleFonts.poppins(
                        fontSize: 16,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      'Tap to open in browser',
                      style: GoogleFonts.poppins(
                        fontSize: 12,
                        color: Colors.grey.shade600,
                      ),
                    ),
                  ],
                ),
              ),
              const Icon(Icons.arrow_forward_ios, size: 16),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildQuickLinkCard(String title, IconData icon, Color color, VoidCallback onTap) {
    return Card(
      elevation: 2,
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(12),
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            children: [
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: color.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Icon(icon, color: color, size: 32),
              ),
              const SizedBox(height: 12),
              Text(
                title,
                textAlign: TextAlign.center,
                style: GoogleFonts.poppins(
                  fontSize: 14,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
