import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';

class CustomNavigationBar extends StatelessWidget {
  final int currentIndex;
  final Function(int) onTap;

  const CustomNavigationBar({
    super.key,
    required this.currentIndex,
    required this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;

    return Container(
      decoration: BoxDecoration(
        color:
            isDarkMode
                ? const Color(0xFF1A1A2E) // Deep blue-black
                : Colors.white,
        boxShadow: [
          BoxShadow(
            color:
                isDarkMode
                    ? const Color(0xFF3F51B5).withAlpha(60) // Indigo shadow
                    : Colors.black.withAlpha(20),
            blurRadius: isDarkMode ? 15 : 10,
            spreadRadius: isDarkMode ? 1 : 0,
            offset: const Offset(0, -3),
          ),
        ],
        borderRadius: const BorderRadius.only(
          topLeft: Radius.circular(24),
          topRight: Radius.circular(24),
        ),
        border:
            isDarkMode
                ? Border(
                  top: BorderSide(
                    color: const Color(0xFF303045).withAlpha(150),
                    width: 1.5,
                  ),
                )
                : null,
      ),
      child: SafeArea(
        child: Padding(
          padding: const EdgeInsets.symmetric(horizontal: 8.0, vertical: 8.0),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceAround,
            children: [
              _buildNavItem(
                context,
                0,
                Icons.home_rounded,
                Icons.home_outlined,
                'Home',
              ),
              _buildNavItem(
                context,
                1,
                Icons.book_rounded,
                Icons.book_outlined,
                'Books',
              ),
              _buildNavItem(
                context,
                2,
                Icons.note_alt_rounded,
                Icons.note_alt_outlined,
                'Notes',
              ),
              _buildNavItem(
                context,
                3,
                Icons.build_rounded,
                Icons.build_outlined,
                'Tools',
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildNavItem(
    BuildContext context,
    int index,
    IconData selectedIcon,
    IconData unselectedIcon,
    String label,
  ) {
    final isSelected = currentIndex == index;
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;

    // Use different colors for dark mode
    final Color primaryColor =
        isDarkMode
            ? const Color(0xFF2196F3) // Brighter blue for dark mode
            : Theme.of(context).colorScheme.primary;

    final Color accentColor =
        isDarkMode
            ? const Color(0xFF03DAC6) // Teal accent for dark mode
            : Theme.of(context).colorScheme.secondary;

    final Color unselectedColor =
        isDarkMode ? Colors.white.withAlpha(150) : Colors.black54;

    return GestureDetector(
      onTap: () => onTap(index),
      child: AnimatedContainer(
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeInOut,
        padding: EdgeInsets.symmetric(
          horizontal: isSelected ? 18.0 : 12.0,
          vertical: 10.0,
        ),
        decoration: BoxDecoration(
          color:
              isSelected
                  ? isDarkMode
                      ? primaryColor.withAlpha(40)
                      : primaryColor.withAlpha(30)
                  : Colors.transparent,
          borderRadius: BorderRadius.circular(20),
          border:
              isSelected && isDarkMode
                  ? Border.all(color: primaryColor.withAlpha(100), width: 1.5)
                  : null,
          boxShadow:
              isSelected
                  ? [
                    BoxShadow(
                      color:
                          isDarkMode
                              ? primaryColor.withAlpha(60)
                              : primaryColor.withAlpha(30),
                      blurRadius: isDarkMode ? 12 : 8,
                      spreadRadius: isDarkMode ? 1 : 0,
                      offset: const Offset(0, 2),
                    ),
                  ]
                  : null,
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            TweenAnimationBuilder<double>(
              tween: Tween<double>(begin: 0, end: isSelected ? 1.0 : 0.0),
              duration: const Duration(milliseconds: 300),
              curve: Curves.elasticOut,
              builder: (context, value, child) {
                return Transform.scale(
                  scale: 1.0 + (value * 0.2),
                  child: Icon(
                    isSelected ? selectedIcon : unselectedIcon,
                    color:
                        isSelected
                            ? isDarkMode
                                ? accentColor // Use teal accent for selected icons in dark mode
                                : primaryColor
                            : unselectedColor,
                    size: isDarkMode ? 26 : 24,
                  ),
                );
              },
            ),
            if (isSelected) ...[
              const SizedBox(width: 8),
              Text(
                label,
                style: GoogleFonts.poppins(
                  fontSize: isDarkMode ? 15 : 14,
                  fontWeight: FontWeight.w600,
                  letterSpacing: 0.5,
                  color:
                      isDarkMode
                          ? accentColor
                          : primaryColor, // Use teal accent for text in dark mode
                  shadows:
                      isDarkMode
                          ? [
                            Shadow(
                              color: accentColor.withAlpha(100),
                              blurRadius: 2,
                              offset: const Offset(0, 1),
                            ),
                          ]
                          : null,
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }
}
