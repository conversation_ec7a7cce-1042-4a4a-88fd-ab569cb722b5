import 'package:flutter/material.dart';
import 'dart:async';
import 'package:google_fonts/google_fonts.dart';
import 'main_layout.dart';
import 'constants.dart';

class SplashScreen extends StatefulWidget {
  const SplashScreen({super.key});

  @override
  State<SplashScreen> createState() => _SplashScreenState();
}

class _SplashScreenState extends State<SplashScreen> with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;
  
  @override
  void initState() {
    super.initState();
    
    // Setup fade-in animation
    _animationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 1500),
    );
    
    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeIn,
    ));
    
    // Start animation
    _animationController.forward();
    
    // Navigate to main layout after delay
    Timer(const Duration(seconds: 2), () {
      Navigator.of(context).pushReplacement(
        MaterialPageRoute(builder: (context) => const MainLayout()),
      );
    });
  }
  
  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    // Get the current theme
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;

    // Background color based on theme
    final backgroundColor =
        isDarkMode
            ? const Color(0xFF0A0A1A) // Dark theme background
            : Colors.white; // Light theme background

    return Scaffold(
      backgroundColor: backgroundColor,
      body: FadeTransition(
        opacity: _fadeAnimation,
        child: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              // Splash image with reduced size (54% of original - reduced by 10%)
              SizedBox(
                width: MediaQuery.of(context).size.width * 0.54, // 54% of screen width (10% smaller than before)
                child: Image.asset(
                  'assets/images/splash_screen.png',
                  fit: BoxFit.contain,
                ),
              ),
              const SizedBox(height: 24), // Padding between image and text
              // App name text with fade animation
              Text(
                'Nepali Results',
                style: GoogleFonts.poppins(
                  fontSize: 34, // Increased by 10% from 31 (originally 28, now 34 for better visibility)
                  fontWeight: FontWeight.bold,
                  color: Colors.red, // Red color as requested (#FF0000)
                  letterSpacing: 0.8, // Increased for better clarity
                ),
              ),
              const SizedBox(height: 8), // Spacing between title and subtitle
              // Subtitle text with fade animation
              Text(
                'SEE, HSEB, TU, KU, PU, CTEVT, DV, IPO',
                style: GoogleFonts.poppins(
                  fontSize: 14,
                  fontWeight: FontWeight.w500,
                  color: isDarkMode ? Colors.white70 : Colors.black87,
                  letterSpacing: 0.5,
                ),
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
      ),
    );
  }
}
