import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'unified_gpa_calculator_screen.dart';
import 'see_gpa_calculator_screen.dart';
import 'tools/nepali_date_converter.dart';
import 'tools/basic_calculator.dart';
import 'tools/age_calculator.dart';

class ToolsPage extends StatelessWidget {
  const ToolsPage({super.key});

  @override
  Widget build(BuildContext context) {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;

    return Scaffold(
      appBar: AppBar(
        title: Text(
          'Tools',
          style: GoogleFonts.poppins(fontWeight: FontWeight.bold),
        ),
        elevation: 0,
      ),
      body: SafeArea(
        child: SingleChildScrollView(
          physics: const BouncingScrollPhysics(),
          child: Padding(
            padding: const EdgeInsets.all(16.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Educational Tools Section
                Text(
                  'Educational Tools',
                  style: GoogleFonts.poppins(
                    fontSize: 20,
                    fontWeight: FontWeight.bold,
                    color: Theme.of(context).colorScheme.primary,
                  ),
                ),
                const SizedBox(height: 16),

                // Educational Tools Grid
                Row(
                  children: [
                    Expanded(
                      child: _buildToolCard(
                        context,
                        'NEB GPA Calculator',
                        Icons.calculate_rounded,
                        Colors.purple,
                        () {
                          Navigator.push(
                            context,
                            MaterialPageRoute(
                              builder:
                                  (context) =>
                                      const UnifiedGpaCalculatorScreen(),
                            ),
                          );
                        },
                      ),
                    ),
                    const SizedBox(width: 16),
                    Expanded(
                      child: _buildToolCard(
                        context,
                        'SEE GPA Calculator',
                        Icons.school_rounded,
                        Colors.blue,
                        () {
                          Navigator.push(
                            context,
                            MaterialPageRoute(
                              builder:
                                  (context) => const SeeGpaCalculatorScreen(),
                            ),
                          );
                        },
                      ),
                    ),
                  ],
                ),

                const SizedBox(height: 32),

                // Digital Tools Section
                Text(
                  'Digital Tools',
                  style: GoogleFonts.poppins(
                    fontSize: 20,
                    fontWeight: FontWeight.bold,
                    color: Theme.of(context).colorScheme.primary,
                  ),
                ),
                const SizedBox(height: 16),

                // Digital Tools Grid
                Column(
                  children: [
                    Row(
                      children: [
                        Expanded(
                          child: _buildToolCard(
                            context,
                            'Nepali Date Converter',
                            Icons.calendar_today_rounded,
                            Colors.indigo,
                            () {
                              Navigator.push(
                                context,
                                MaterialPageRoute(
                                  builder: (context) => const NepaliDateConverter(),
                                ),
                              );
                            },
                          ),
                        ),
                        const SizedBox(width: 16),
                        Expanded(
                          child: _buildToolCard(
                            context,
                            'Basic Calculator',
                            Icons.calculate,
                            Colors.teal,
                            () {
                              Navigator.push(
                                context,
                                MaterialPageRoute(
                                  builder: (context) => const BasicCalculator(),
                                ),
                              );
                            },
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 16),
                    Row(
                      children: [
                        Expanded(
                          child: _buildToolCard(
                            context,
                            'Age Calculator',
                            Icons.cake_rounded,
                            Colors.amber,
                            () {
                              Navigator.push(
                                context,
                                MaterialPageRoute(
                                  builder: (context) => const AgeCalculator(),
                                ),
                              );
                            },
                          ),
                        ),
                        const SizedBox(width: 16),
                        Expanded(child: Container()), // Empty container for layout balance
                      ],
                    ),
                  ],
                ),

                const SizedBox(height: 32),

                // Coming Soon Section
                Text(
                  'Coming Soon',
                  style: GoogleFonts.poppins(
                    fontSize: 20,
                    fontWeight: FontWeight.bold,
                    color: Theme.of(context).colorScheme.primary,
                  ),
                ),
                const SizedBox(height: 16),

                // Coming Soon Tools
                Row(
                  children: [
                    Expanded(
                      child: _buildToolCard(
                        context,
                        'Subject Tracker',
                        Icons.track_changes_outlined,
                        Colors.deepPurple,
                        () {
                          ScaffoldMessenger.of(context).showSnackBar(
                            const SnackBar(
                              content: Text('Coming soon!'),
                              duration: Duration(seconds: 2),
                            ),
                          );
                        },
                      ),
                    ),
                    const SizedBox(width: 16),
                    Expanded(
                      child: _buildToolCard(
                        context,
                        'Nepali Dictionary',
                        Icons.translate,
                        Colors.orange,
                        () {
                          ScaffoldMessenger.of(context).showSnackBar(
                            const SnackBar(
                              content: Text('Coming soon!'),
                              duration: Duration(seconds: 2),
                            ),
                          );
                        },
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildToolCard(
    BuildContext context,
    String title,
    IconData icon,
    Color iconColor,
    VoidCallback onTap,
  ) {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;

    return Card(
      elevation: isDarkMode ? 8 : 4,
      shadowColor:
          isDarkMode ? iconColor.withAlpha(100) : iconColor.withAlpha(60),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
        side:
            isDarkMode
                ? BorderSide(color: iconColor.withAlpha(80), width: 1)
                : BorderSide.none,
      ),
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(16),
        splashColor: iconColor.withAlpha(50),
        highlightColor: iconColor.withAlpha(30),
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              // Icon container
              Container(
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color:
                      isDarkMode
                          ? iconColor.withAlpha(40)
                          : iconColor.withAlpha(25),
                  shape: BoxShape.circle,
                  boxShadow: [
                    BoxShadow(
                      color:
                          isDarkMode
                              ? iconColor.withAlpha(80)
                              : iconColor.withAlpha(40),
                      blurRadius: isDarkMode ? 10 : 6,
                      spreadRadius: isDarkMode ? 1 : 0,
                      offset: const Offset(0, 3),
                    ),
                  ],
                ),
                child: Icon(icon, color: iconColor, size: 32),
              ),
              const SizedBox(height: 16),
              // Title
              Text(
                title,
                textAlign: TextAlign.center,
                style: GoogleFonts.poppins(
                  fontSize: isDarkMode ? 16 : 15,
                  fontWeight: FontWeight.bold,
                  color: isDarkMode ? Colors.white : Colors.black87,
                  letterSpacing: 0.3,
                  shadows:
                      isDarkMode
                          ? [
                            Shadow(
                              color: iconColor.withAlpha(100),
                              blurRadius: 2,
                              offset: const Offset(0, 1),
                            ),
                          ]
                          : null,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
