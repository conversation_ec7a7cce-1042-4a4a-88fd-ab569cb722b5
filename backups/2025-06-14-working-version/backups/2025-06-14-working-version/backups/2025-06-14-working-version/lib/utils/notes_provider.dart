import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../models/note.dart';

class NotesProvider extends ChangeNotifier {
  List<Note> _notes = [];
  Note? _currentNote;
  bool _isLoading = false;
  static const String _storageKey = 'nepali_results_notes';

  // Getters
  List<Note> get notes => _notes;
  Note? get currentNote => _currentNote;
  bool get isLoading => _isLoading;

  // Constructor - Load notes from storage
  NotesProvider() {
    _loadNotes();
  }

  // Load notes from SharedPreferences
  Future<void> _loadNotes() async {
    _setLoading(true);
    try {
      final prefs = await SharedPreferences.getInstance();
      final notesJson = prefs.getStringList(_storageKey);
      
      if (notesJson != null) {
        _notes = notesJson
            .map((noteJson) => Note.fromJson(json.decode(noteJson)))
            .toList();
        
        // Sort notes by updated date (newest first)
        _notes.sort((a, b) => b.updatedAt.compareTo(a.updatedAt));
      }
      
      // Set current note if notes exist
      if (!_notes.isEmpty) {
        _currentNote = _notes.first;
      }
    } catch (e) {
      debugPrint('Error loading notes: $e');
      _createDefaultNote();
    }
    _setLoading(false);
  }

  // Create a default note if needed
  void _createDefaultNote() {
    final defaultNote = Note.create(
      title: 'New Note',
      content: '',
    );
    _notes = [defaultNote];
    _currentNote = defaultNote;
  }

  // Save notes to SharedPreferences
  Future<void> _saveNotes() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final notesJson = _notes.map((note) => json.encode(note.toJson())).toList();
      await prefs.setStringList(_storageKey, notesJson);
    } catch (e) {
      debugPrint('Error saving notes: $e');
    }
  }

  // Set loading state
  void _setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }

  // Create a new note
  Future<void> createNote() async {
    final newNote = Note.create();
    _notes.insert(0, newNote);
    _currentNote = newNote;
    await _saveNotes();
    notifyListeners();
  }

  // Update the current note
  Future<void> updateCurrentNote({String? title, String? content}) async {
    if (_currentNote == null) return;
    
    final index = _notes.indexWhere((note) => note.id == _currentNote!.id);
    if (index == -1) return;
    
    _currentNote = _currentNote!.copyWith(
      title: title,
      content: content,
    );
    
    _notes[index] = _currentNote!;
    
    // Re-sort notes by updated date
    _notes.sort((a, b) => b.updatedAt.compareTo(a.updatedAt));
    
    await _saveNotes();
    notifyListeners();
  }

  // Delete a note
  Future<void> deleteNote(String id) async {
    _notes.removeWhere((note) => note.id == id);
    
    // If the current note was deleted, select the first note
    if (_currentNote != null && _currentNote!.id == id) {
      _currentNote = _notes.isNotEmpty ? _notes.first : null;
      
      // If no notes left, create a default one
      if (_currentNote == null) {
        _createDefaultNote();
      }
    }
    
    await _saveNotes();
    notifyListeners();
  }

  // Select a note
  void selectNote(String id) {
    final note = _notes.firstWhere(
      (note) => note.id == id,
      orElse: () => _notes.first,
    );
    _currentNote = note;
    notifyListeners();
  }
}
