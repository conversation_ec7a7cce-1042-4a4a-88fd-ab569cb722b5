import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import '../models/result_category.dart';
import '../utils/constants.dart';

class CategoryCard extends StatelessWidget {
  final ResultCategory category;
  final VoidCallback onTap;

  const CategoryCard({super.key, required this.category, required this.onTap});

  @override
  Widget build(BuildContext context) {
    // Get category-specific color or use primary color as fallback
    final categoryColor =
        AppConstants.categoryColors[category.name] ??
        Theme.of(context).colorScheme.primary;

    return Card(
      margin: const EdgeInsets.all(2),
      elevation: 1.0,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(12),
        child: Container(
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(12),
            color: categoryColor.withAlpha(15),
          ),
          child: Padding(
            padding: const EdgeInsets.symmetric(horizontal: 4.0, vertical: 8.0),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                // Icon with colored background
                Container(
                  padding: const EdgeInsets.all(10),
                  decoration: BoxDecoration(
                    color: categoryColor.withAlpha(40),
                    shape: BoxShape.circle,
                  ),
                  child: Icon(
                    category.icon,
                    color: categoryColor,
                    size: 40,
                  ), // 250% increase from 16
                ),
                const SizedBox(height: 8),

                // Category name
                Text(
                  category.name,
                  style: GoogleFonts.poppins(
                    textStyle: TextStyle(
                      fontSize: 15,
                      fontWeight: FontWeight.w700,
                      letterSpacing: 0.3,
                      color:
                          Theme.of(context).brightness == Brightness.dark
                              ? Colors.white
                              : Colors.black87,
                    ),
                  ),
                  textAlign: TextAlign.center,
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
