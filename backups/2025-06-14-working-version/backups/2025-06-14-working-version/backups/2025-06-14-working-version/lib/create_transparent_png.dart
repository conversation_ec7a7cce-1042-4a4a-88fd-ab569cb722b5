import 'dart:io';
import 'package:image/image.dart' as img;

// This script creates a transparent PNG image for the native splash screen
// Run with: dart lib/tools/create_transparent_png.dart

void main() async {
  // Create a 1x1 transparent image
  final image = img.Image(width: 1, height: 1);

  // Make it transparent
  image.clear(img.ColorRgba8(0, 0, 0, 0));

  // Encode to PNG
  final pngData = img.encodePng(image);

  // Save to file
  final directory = Directory('assets/images');
  if (!await directory.exists()) {
    await directory.create(recursive: true);
  }

  final file = File('${directory.path}/transparent.png');
  await file.writeAsBytes(pngData);

  print('Created transparent PNG at: ${file.path}');
}
