import 'package:flutter/material.dart';

void main() {
  runApp(const MyApp());
}

class MyApp extends StatelessWidget {
  const MyApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: 'Nepali Results',
      theme: ThemeData(
        colorScheme: ColorScheme.fromSeed(seedColor: Colors.blue),
        useMaterial3: true,
      ),
      home: const HomeScreen(),
      debugShowCheckedModeBanner: false,
    );
  }
}

class HomeScreen extends StatelessWidget {
  const HomeScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        backgroundColor: Colors.blue.shade700,
        foregroundColor: Colors.white,
        title: const Text(
          '२०८१ साल जेठ १, बुधबार',
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.normal,
            color: Colors.white,
          ),
        ),
        centerTitle: true,
        actions: [
          IconButton(
            icon: const Icon(Icons.notifications),
            onPressed: () {},
          ),
          IconButton(
            icon: const Icon(Icons.dark_mode),
            onPressed: () {},
          ),
          PopupMenuButton<String>(
            icon: const Icon(Icons.more_vert),
            onSelected: (value) {},
            itemBuilder: (BuildContext context) {
              return [
                const PopupMenuItem<String>(
                  value: 'About App',
                  child: Text('About App'),
                ),
              ];
            },
          ),
        ],
      ),
      body: SafeArea(
        child: Column(
          children: [
            // Curved header with light blue background
            Container(
              height: 120,
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  begin: Alignment.topCenter,
                  end: Alignment.bottomCenter,
                  colors: [
                    Colors.lightBlue.shade200,
                    Colors.lightBlue.shade50,
                  ],
                ),
                borderRadius: const BorderRadius.only(
                  bottomLeft: Radius.circular(30),
                  bottomRight: Radius.circular(30),
                ),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withOpacity(0.1),
                    blurRadius: 8,
                    spreadRadius: 0,
                    offset: const Offset(0, 4),
                  ),
                ],
              ),
              child: Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Text(
                      'Nepali Results',
                      style: TextStyle(
                        fontSize: 28,
                        fontWeight: FontWeight.bold,
                        letterSpacing: 0.5,
                        color: Colors.blue.shade800,
                      ),
                    ),
                    const SizedBox(height: 8),
                    Text(
                      "Check your results easily",
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.w500,
                        letterSpacing: 0.5,
                        color: Colors.blue.shade600,
                      ),
                    ),
                  ],
                ),
              ),
            ),
            const SizedBox(height: 16),
            
            // Grid of result categories
            Expanded(
              child: SingleChildScrollView(
                padding: const EdgeInsets.all(16),
                child: Column(
                  children: [
                    _buildResultCategoryCard(
                      context,
                      'SEE Results',
                      'Secondary Education Examination Results',
                      Colors.blue,
                    ),
                    _buildResultCategoryCard(
                      context,
                      'HSEB Results',
                      'Higher Secondary Education Board Results',
                      Colors.indigo,
                    ),
                    _buildResultCategoryCard(
                      context,
                      'TU Results',
                      'Tribhuvan University Results',
                      Colors.deepPurple,
                    ),
                    _buildResultCategoryCard(
                      context,
                      'KU Results',
                      'Kathmandu University Results',
                      Colors.teal,
                    ),
                    _buildResultCategoryCard(
                      context,
                      'PU Results',
                      'Purbanchal University Results',
                      Colors.lightGreen,
                    ),
                    _buildResultCategoryCard(
                      context,
                      'CTEVT Results',
                      'Council for Technical Education and Vocational Training Results',
                      Colors.orange,
                    ),
                    _buildResultCategoryCard(
                      context,
                      'DV Lottery Results',
                      'Diversity Visa Lottery Results',
                      Colors.green,
                    ),
                    _buildResultCategoryCard(
                      context,
                      'IPO Results',
                      'Initial Public Offering Results',
                      Colors.amber,
                      isHighlighted: true,
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
      bottomNavigationBar: BottomNavigationBar(
        currentIndex: 0,
        type: BottomNavigationBarType.fixed,
        selectedItemColor: Colors.blue,
        unselectedItemColor: Colors.grey,
        items: const [
          BottomNavigationBarItem(icon: Icon(Icons.home), label: 'Home'),
          BottomNavigationBarItem(icon: Icon(Icons.book), label: 'Books'),
          BottomNavigationBarItem(icon: Icon(Icons.calculate), label: 'Tools'),
          BottomNavigationBarItem(icon: Icon(Icons.note), label: 'Notes'),
        ],
      ),
    );
  }

  Widget _buildResultCategoryCard(
    BuildContext context,
    String title,
    String subtitle,
    Color color, {
    bool isHighlighted = false,
  }) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 16),
      child: Card(
        elevation: 4,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(20),
          side: isHighlighted
              ? BorderSide(color: Colors.amber.shade700, width: 2)
              : BorderSide.none,
        ),
        child: InkWell(
          onTap: () => _navigateToResultDetails(context, title),
          borderRadius: BorderRadius.circular(20),
          child: Container(
            padding: const EdgeInsets.all(16),
            child: Row(
              children: [
                Container(
                  width: 50,
                  height: 50,
                  decoration: BoxDecoration(
                    color: color.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(10),
                  ),
                  child: Center(
                    child: Icon(
                      Icons.description,
                      color: color,
                      size: 30,
                    ),
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        title,
                        style: const TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        subtitle,
                        style: TextStyle(
                          fontSize: 12,
                          color: Colors.grey.shade600,
                        ),
                      ),
                    ],
                  ),
                ),
                const Icon(Icons.chevron_right),
              ],
            ),
          ),
        ),
      ),
    );
  }

  void _navigateToResultDetails(BuildContext context, String category) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => ResultDetailsScreen(category: category),
      ),
    );
  }
}

class ResultDetailsScreen extends StatelessWidget {
  final String category;

  const ResultDetailsScreen({
    super.key,
    required this.category,
  });

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        backgroundColor: _getCategoryColor(),
        foregroundColor: Colors.white,
        title: Text(category),
        centerTitle: true,
      ),
      body: SafeArea(
        child: SingleChildScrollView(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Description
              Container(
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: _getCategoryColor().withOpacity(0.1),
                  borderRadius: BorderRadius.circular(16),
                ),
                child: Text(
                  _getCategoryDescription(),
                  style: const TextStyle(
                    fontSize: 16,
                    height: 1.5,
                  ),
                ),
              ),
              const SizedBox(height: 24),
              
              // Result Links
              const Text(
                'Check Results',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 16),
              ..._buildResultLinks(context),
            ],
          ),
        ),
      ),
    );
  }

  Color _getCategoryColor() {
    switch (category) {
      case 'SEE Results':
        return Colors.blue;
      case 'HSEB Results':
        return Colors.indigo;
      case 'TU Results':
        return Colors.deepPurple;
      case 'KU Results':
        return Colors.teal;
      case 'PU Results':
        return Colors.lightGreen;
      case 'CTEVT Results':
        return Colors.orange;
      case 'DV Lottery Results':
        return Colors.green;
      case 'IPO Results':
        return Colors.amber;
      default:
        return Colors.blue;
    }
  }

  String _getCategoryDescription() {
    switch (category) {
      case 'SEE Results':
        return 'Check Secondary Education Examination results from official sources.';
      case 'HSEB Results':
        return 'Access Higher Secondary Education Board examination results.';
      case 'TU Results':
        return 'Find Tribhuvan University examination results for various faculties and programs.';
      case 'KU Results':
        return 'Check Kathmandu University results for undergraduate and graduate programs.';
      case 'PU Results':
        return 'Access Purbanchal University examination results.';
      case 'CTEVT Results':
        return 'Find Council for Technical Education and Vocational Training examination results.';
      case 'DV Lottery Results':
        return 'Check your Diversity Visa Lottery application status.';
      case 'IPO Results':
        return 'Check Initial Public Offering (IPO) allotment results from CDSC and access Mero Share portal.';
      default:
        return 'Check your results online.';
    }
  }

  List<Widget> _buildResultLinks(BuildContext context) {
    final List<Map<String, dynamic>> links = _getResultLinks();
    
    return links.map((link) {
      return Padding(
        padding: const EdgeInsets.only(bottom: 12),
        child: _buildLinkCard(
          context,
          title: link['title'],
          subtitle: link['subtitle'],
          color: link['color'],
          url: link['url'],
        ),
      );
    }).toList();
  }

  Widget _buildLinkCard(
    BuildContext context, {
    required String title,
    required String subtitle,
    required Color color,
    required String url,
  }) {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: InkWell(
        onTap: () {
          // In a real app, you would open the URL here
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(content: Text('Opening $title: $url')),
          );
        },
        borderRadius: BorderRadius.circular(12),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Row(
            children: [
              Container(
                width: 40,
                height: 40,
                decoration: BoxDecoration(
                  color: color.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Center(
                  child: Icon(
                    Icons.language,
                    color: color,
                  ),
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      title,
                      style: const TextStyle(
                        fontWeight: FontWeight.bold,
                        fontSize: 16,
                      ),
                    ),
                    Text(
                      subtitle,
                      style: TextStyle(
                        color: Colors.grey.shade600,
                        fontSize: 14,
                      ),
                    ),
                  ],
                ),
              ),
              Icon(
                Icons.open_in_new,
                color: color,
              ),
            ],
          ),
        ),
      ),
    );
  }

  List<Map<String, dynamic>> _getResultLinks() {
    switch (category) {
      case 'IPO Results':
        return [
          {
            'title': 'CDSC IPO Result',
            'subtitle': 'Central Depository System',
            'color': Colors.amber,
            'url': 'https://iporesult.cdsc.com.np/',
          },
          {
            'title': 'Mero Share',
            'subtitle': 'CDSC Mero Share Portal',
            'color': Colors.amber.shade800,
            'url': 'https://meroshare.cdsc.com.np/',
          },
        ];
      default:
        return [
          {
            'title': 'Official Website',
            'subtitle': 'Check results online',
            'color': _getCategoryColor(),
            'url': 'https://example.com',
          },
        ];
    }
  }
}
