import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:translator/translator.dart';
import '../../utils/romanized_nepali_converter.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'dart:convert';

class NepaliDictionary extends StatefulWidget {
  const NepaliDictionary({super.key});

  @override
  State<NepaliDictionary> createState() => _NepaliDictionaryState();
}

class _NepaliDictionaryState extends State<NepaliDictionary>
    with SingleTickerProviderStateMixin {
  // Tab controller
  late TabController _tabController;

  // Text controllers
  final TextEditingController _englishController = TextEditingController();
  final TextEditingController _nepaliController = TextEditingController();

  // Translator
  final GoogleTranslator _translator = GoogleTranslator();

  // Translation results
  String _englishToNepaliResult = '';
  String _nepaliToEnglishResult = '';

  // Loading states
  bool _isLoadingEnToNe = false;
  bool _isLoadingNeToEn = false;

  // Error messages
  String _errorEnToNe = '';
  String _errorNeToEn = '';

  // Input type detection
  String _detectedInputType =
      'unknown'; // 'unknown', 'romanized', or 'devanagari'

  // Favorite translations
  List<Map<String, dynamic>> _favorites = [];

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);
    _loadFavorites();
  }

  @override
  void dispose() {
    _tabController.dispose();
    _englishController.dispose();
    _nepaliController.dispose();
    super.dispose();
  }

  // Load favorites from shared preferences
  Future<void> _loadFavorites() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final favoritesJson = prefs.getString('favorites') ?? '[]';
      setState(() {
        _favorites = List<Map<String, dynamic>>.from(
          jsonDecode(favoritesJson) as List,
        );
      });
    } catch (e) {
      debugPrint('Error loading favorites: $e');
    }
  }

  // Save favorites to shared preferences
  Future<void> _saveFavorites() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString('favorites', jsonEncode(_favorites));
    } catch (e) {
      debugPrint('Error saving favorites: $e');
    }
  }

  // Add to favorites
  void _addToFavorites({
    required String source,
    required String translated,
    required String sourceLanguage,
    required String targetLanguage,
  }) {
    // Check if already in favorites
    final existingIndex = _favorites.indexWhere(
      (fav) => fav['source'] == source && fav['translated'] == translated,
    );

    if (existingIndex != -1) {
      // Already in favorites, show message
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Already in favorites'),
          duration: Duration(seconds: 2),
        ),
      );
      return;
    }

    // Add to favorites
    setState(() {
      _favorites.add({
        'source': source,
        'translated': translated,
        'sourceLanguage': sourceLanguage,
        'targetLanguage': targetLanguage,
        'timestamp': DateTime.now().toIso8601String(),
      });
    });

    // Save favorites
    _saveFavorites();

    // Show message
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Added to favorites'),
        duration: Duration(seconds: 2),
      ),
    );
  }

  // Remove from favorites
  void _removeFromFavorites(int index) {
    setState(() {
      _favorites.removeAt(index);
    });

    // Save favorites
    _saveFavorites();

    // Show message
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Removed from favorites'),
        duration: Duration(seconds: 2),
      ),
    );
  }

  // Translate English to Nepali
  Future<void> _translateEnglishToNepali() async {
    final text = _englishController.text.trim();

    if (text.isEmpty) {
      setState(() {
        _errorEnToNe = 'Please enter text to translate';
        _englishToNepaliResult = '';
      });
      return;
    }

    setState(() {
      _isLoadingEnToNe = true;
      _errorEnToNe = '';
      _englishToNepaliResult = '';
    });

    try {
      final translation = await _translator.translate(
        text,
        from: 'en',
        to: 'ne',
      );

      setState(() {
        _englishToNepaliResult = translation.text;
        _isLoadingEnToNe = false;
      });
    } catch (e) {
      setState(() {
        _errorEnToNe = 'Translation failed: $e';
        _isLoadingEnToNe = false;
      });
    }
  }

  // Translate Nepali to English
  Future<void> _translateNepaliToEnglish() async {
    final text = _nepaliController.text.trim();

    if (text.isEmpty) {
      setState(() {
        _errorNeToEn = 'Please enter text to translate';
        _nepaliToEnglishResult = '';
      });
      return;
    }

    setState(() {
      _isLoadingNeToEn = true;
      _errorNeToEn = '';
      _nepaliToEnglishResult = '';
    });

    try {
      // Detect the script type (Devanagari or Romanized)
      String scriptType = RomanizedNepaliConverter.detectScriptType(text);
      String textToTranslate = text;
      String originalInput = text;

      // Update the detected input type
      setState(() {
        _detectedInputType = scriptType;
      });

      // If the text is primarily Romanized Nepali, convert it to Devanagari script
      if (scriptType == 'romanized') {
        // Convert Romanized Nepali to Devanagari script
        textToTranslate = RomanizedNepaliConverter.convertToDevanagari(text);

        // Only update the text field if the conversion produced a different result
        if (textToTranslate != text) {
          // Save the current cursor position
          final currentPosition = _nepaliController.selection.baseOffset;

          // Update the text field with the converted text for user feedback
          setState(() {
            _nepaliController.text = textToTranslate;

            // Try to maintain a reasonable cursor position
            if (currentPosition > 0 && currentPosition <= text.length) {
              // Calculate a proportional position in the new text
              final newPosition =
                  (currentPosition / text.length * textToTranslate.length)
                      .round();
              _nepaliController.selection = TextSelection.fromPosition(
                TextPosition(
                  offset: newPosition.clamp(0, textToTranslate.length),
                ),
              );
            } else {
              // If cursor position is invalid, place it at the end
              _nepaliController.selection = TextSelection.fromPosition(
                TextPosition(offset: textToTranslate.length),
              );
            }
          });

          // Show a message about the conversion with the original input
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('Converted "$originalInput" to Devanagari script'),
              duration: const Duration(seconds: 2),
              action: SnackBarAction(
                label: 'Undo',
                onPressed: () {
                  setState(() {
                    _nepaliController.text = originalInput;
                    _nepaliController.selection = TextSelection.fromPosition(
                      TextPosition(offset: originalInput.length),
                    );
                  });
                },
              ),
            ),
          );
        }
      }

      // Translate the Nepali text (either original Devanagari or converted) to English
      final translation = await _translator.translate(
        textToTranslate,
        from: 'ne',
        to: 'en',
      );

      setState(() {
        _nepaliToEnglishResult = translation.text;
        _isLoadingNeToEn = false;
      });
    } catch (e) {
      setState(() {
        _errorNeToEn = 'Translation failed: $e';
        _isLoadingNeToEn = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;

    return Scaffold(
      appBar: AppBar(
        title: Text(
          'Nepali Translate',
          style: GoogleFonts.poppins(fontWeight: FontWeight.bold),
        ),
        elevation: 0,
        bottom: TabBar(
          controller: _tabController,
          labelStyle: GoogleFonts.poppins(fontWeight: FontWeight.bold),
          tabs: const [
            Tab(text: 'English to Nepali'),
            Tab(text: 'Nepali to English'),
            Tab(text: 'Favorites'),
          ],
        ),
      ),
      body: TabBarView(
        controller: _tabController,
        children: [
          // English to Nepali Tab
          SingleChildScrollView(
            physics: const BouncingScrollPhysics(),
            child: Padding(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // English input
                  TextField(
                    controller: _englishController,
                    decoration: InputDecoration(
                      labelText: 'Enter English text',
                      hintText: 'Type here...',
                      border: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(12),
                      ),
                      suffixIcon: IconButton(
                        icon: const Icon(Icons.clear),
                        onPressed: () {
                          _englishController.clear();
                          setState(() {
                            _englishToNepaliResult = '';
                            _errorEnToNe = '';
                          });
                        },
                      ),
                    ),
                    maxLines: 3,
                  ),

                  if (_errorEnToNe.isNotEmpty)
                    Padding(
                      padding: const EdgeInsets.only(top: 8.0),
                      child: Text(
                        _errorEnToNe,
                        style: GoogleFonts.poppins(
                          color: Colors.red,
                          fontSize: 12,
                        ),
                      ),
                    ),

                  const SizedBox(height: 16),

                  // Translate button
                  SizedBox(
                    width: double.infinity,
                    child: ElevatedButton(
                      onPressed:
                          _isLoadingEnToNe ? null : _translateEnglishToNepali,
                      style: ElevatedButton.styleFrom(
                        backgroundColor: Colors.teal,
                        foregroundColor: Colors.white,
                        padding: const EdgeInsets.symmetric(vertical: 12),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(12),
                        ),
                      ),
                      child:
                          _isLoadingEnToNe
                              ? const SizedBox(
                                height: 20,
                                width: 20,
                                child: CircularProgressIndicator(
                                  strokeWidth: 2,
                                  color: Colors.white,
                                ),
                              )
                              : Text(
                                'Translate to Nepali',
                                style: GoogleFonts.poppins(
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                    ),
                  ),

                  const SizedBox(height: 24),

                  // Result
                  if (_englishToNepaliResult.isNotEmpty) ...[
                    Text(
                      'Translation:',
                      style: GoogleFonts.poppins(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                        color: Colors.teal,
                      ),
                    ),
                    const SizedBox(height: 8),
                    Container(
                      width: double.infinity,
                      padding: const EdgeInsets.all(16),
                      decoration: BoxDecoration(
                        color:
                            isDarkMode
                                ? Colors.teal.withAlpha(40)
                                : Colors.teal.withAlpha(20),
                        borderRadius: BorderRadius.circular(12),
                        border: Border.all(
                          color: Colors.teal.withAlpha(100),
                          width: 1,
                        ),
                      ),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            _englishToNepaliResult,
                            style: GoogleFonts.poppins(
                              fontSize: 18,
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                          const SizedBox(height: 16),
                          Row(
                            mainAxisAlignment: MainAxisAlignment.end,
                            children: [
                              // Add to favorites button
                              IconButton(
                                icon: const Icon(Icons.favorite_border),
                                color: Colors.teal,
                                onPressed: () {
                                  _addToFavorites(
                                    source: _englishController.text,
                                    translated: _englishToNepaliResult,
                                    sourceLanguage: 'English',
                                    targetLanguage: 'Nepali',
                                  );
                                },
                              ),
                            ],
                          ),
                        ],
                      ),
                    ),
                  ],
                ],
              ),
            ),
          ),

          // Nepali to English Tab
          SingleChildScrollView(
            physics: const BouncingScrollPhysics(),
            child: Padding(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Nepali input instruction
                  Container(
                    width: double.infinity,
                    padding: const EdgeInsets.all(12),
                    margin: const EdgeInsets.only(bottom: 16),
                    decoration: BoxDecoration(
                      color:
                          isDarkMode
                              ? Colors.blue.withAlpha(30)
                              : Colors.blue.withAlpha(15),
                      borderRadius: BorderRadius.circular(12),
                      border: Border.all(
                        color: Colors.blue.withAlpha(50),
                        width: 1,
                      ),
                    ),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Row(
                          children: [
                            Icon(
                              Icons.auto_awesome,
                              size: 16,
                              color: Colors.blue,
                            ),
                            const SizedBox(width: 8),
                            Expanded(
                              child: Text(
                                'Smart Input Detection',
                                style: GoogleFonts.poppins(
                                  fontSize: 14,
                                  fontWeight: FontWeight.bold,
                                  color: Colors.blue,
                                ),
                              ),
                            ),
                          ],
                        ),
                        const SizedBox(height: 8),
                        Text(
                          'Type in either Romanized Nepali (English letters) or Traditional Nepali (Devanagari script).',
                          style: GoogleFonts.poppins(fontSize: 13),
                        ),
                        const SizedBox(height: 4),
                        Text(
                          'The app will automatically detect and translate correctly.',
                          style: GoogleFonts.poppins(fontSize: 13),
                        ),
                      ],
                    ),
                  ),

                  // Nepali input
                  Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      TextField(
                        controller: _nepaliController,
                        decoration: InputDecoration(
                          labelText: 'Enter Nepali text',
                          hintText:
                              'Type "Mero Naam KD Ho" or "मेरो नाम केडी हो"',
                          helperText:
                              'Both Romanized and Traditional Nepali supported',
                          helperStyle: GoogleFonts.poppins(
                            fontSize: 12,
                            color: Colors.blue,
                          ),
                          border: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(12),
                          ),
                          prefixIcon: Icon(Icons.translate, color: Colors.blue),
                          suffixIcon: Row(
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              // Input type indicator
                              if (_detectedInputType != 'unknown')
                                Padding(
                                  padding: const EdgeInsets.only(right: 8.0),
                                  child: Container(
                                    padding: const EdgeInsets.symmetric(
                                      horizontal: 8,
                                      vertical: 4,
                                    ),
                                    decoration: BoxDecoration(
                                      color:
                                          _detectedInputType == 'romanized'
                                              ? Colors.orange.withAlpha(50)
                                              : Colors.green.withAlpha(50),
                                      borderRadius: BorderRadius.circular(12),
                                      border: Border.all(
                                        color:
                                            _detectedInputType == 'romanized'
                                                ? Colors.orange
                                                : Colors.green,
                                        width: 1,
                                      ),
                                    ),
                                    child: Text(
                                      _detectedInputType == 'romanized'
                                          ? 'Romanized'
                                          : 'Devanagari',
                                      style: GoogleFonts.poppins(
                                        fontSize: 10,
                                        color:
                                            _detectedInputType == 'romanized'
                                                ? Colors.orange.shade800
                                                : Colors.green.shade800,
                                        fontWeight: FontWeight.bold,
                                      ),
                                    ),
                                  ),
                                ),
                              // Clear button
                              IconButton(
                                icon: const Icon(Icons.clear),
                                onPressed: () {
                                  _nepaliController.clear();
                                  setState(() {
                                    _nepaliToEnglishResult = '';
                                    _errorNeToEn = '';
                                    _detectedInputType = 'unknown';
                                  });
                                },
                              ),
                            ],
                          ),
                        ),
                        maxLines: 3,
                        onChanged: (value) {
                          if (value.isNotEmpty) {
                            setState(() {
                              _detectedInputType =
                                  RomanizedNepaliConverter.detectScriptType(
                                    value,
                                  );
                            });
                          } else {
                            setState(() {
                              _detectedInputType = 'unknown';
                            });
                          }
                        },
                      ),
                    ],
                  ),

                  if (_errorNeToEn.isNotEmpty)
                    Padding(
                      padding: const EdgeInsets.only(top: 8.0),
                      child: Text(
                        _errorNeToEn,
                        style: GoogleFonts.poppins(
                          color: Colors.red,
                          fontSize: 12,
                        ),
                      ),
                    ),

                  const SizedBox(height: 16),

                  // Example Nepali phrases
                  Container(
                    width: double.infinity,
                    padding: const EdgeInsets.all(12),
                    margin: const EdgeInsets.only(bottom: 16),
                    decoration: BoxDecoration(
                      color:
                          isDarkMode
                              ? Colors.blue.withAlpha(30)
                              : Colors.blue.withAlpha(15),
                      borderRadius: BorderRadius.circular(12),
                      border: Border.all(
                        color: Colors.blue.withAlpha(50),
                        width: 1,
                      ),
                    ),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Row(
                          children: [
                            Icon(
                              Icons.info_outline,
                              size: 16,
                              color: Colors.blue,
                            ),
                            const SizedBox(width: 8),
                            Text(
                              'Example Phrases:',
                              style: GoogleFonts.poppins(
                                fontSize: 14,
                                fontWeight: FontWeight.bold,
                                color: Colors.blue,
                              ),
                            ),
                          ],
                        ),
                        const SizedBox(height: 12),

                        // Traditional Nepali examples
                        Text(
                          'Traditional Nepali:',
                          style: GoogleFonts.poppins(
                            fontSize: 13,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        const SizedBox(height: 4),
                        Text(
                          '• "मेरो देश नेपाल हो।" → "My country is Nepal."',
                          style: GoogleFonts.poppins(fontSize: 13),
                        ),
                        Text(
                          '• "तिम्रो नाम के हो?" → "What is your name?"',
                          style: GoogleFonts.poppins(fontSize: 13),
                        ),

                        const SizedBox(height: 12),

                        // Romanized Nepali examples
                        Text(
                          'Romanized Nepali:',
                          style: GoogleFonts.poppins(
                            fontSize: 13,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        const SizedBox(height: 4),
                        Text(
                          '• "Mero Desh Nepal Ho" → "My country is Nepal."',
                          style: GoogleFonts.poppins(fontSize: 13),
                        ),
                        Text(
                          '• "Timro Naam Ke Ho?" → "What is your name?"',
                          style: GoogleFonts.poppins(fontSize: 13),
                        ),
                      ],
                    ),
                  ),

                  // Translate button
                  SizedBox(
                    width: double.infinity,
                    child: ElevatedButton(
                      onPressed:
                          _isLoadingNeToEn ? null : _translateNepaliToEnglish,
                      style: ElevatedButton.styleFrom(
                        backgroundColor: Colors.blue,
                        foregroundColor: Colors.white,
                        padding: const EdgeInsets.symmetric(vertical: 12),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(12),
                        ),
                      ),
                      child:
                          _isLoadingNeToEn
                              ? const SizedBox(
                                height: 20,
                                width: 20,
                                child: CircularProgressIndicator(
                                  strokeWidth: 2,
                                  color: Colors.white,
                                ),
                              )
                              : Text(
                                'Translate to English',
                                style: GoogleFonts.poppins(
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                    ),
                  ),

                  const SizedBox(height: 24),

                  // Result
                  if (_nepaliToEnglishResult.isNotEmpty) ...[
                    Text(
                      'Translation:',
                      style: GoogleFonts.poppins(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                        color: Colors.blue,
                      ),
                    ),
                    const SizedBox(height: 8),
                    Container(
                      width: double.infinity,
                      padding: const EdgeInsets.all(16),
                      decoration: BoxDecoration(
                        color:
                            isDarkMode
                                ? Colors.blue.withAlpha(40)
                                : Colors.blue.withAlpha(20),
                        borderRadius: BorderRadius.circular(12),
                        border: Border.all(
                          color: Colors.blue.withAlpha(100),
                          width: 1,
                        ),
                      ),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            _nepaliToEnglishResult,
                            style: GoogleFonts.poppins(
                              fontSize: 18,
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                          const SizedBox(height: 16),
                          Row(
                            mainAxisAlignment: MainAxisAlignment.end,
                            children: [
                              // Add to favorites button
                              IconButton(
                                icon: const Icon(Icons.favorite_border),
                                color: Colors.blue,
                                onPressed: () {
                                  _addToFavorites(
                                    source: _nepaliController.text,
                                    translated: _nepaliToEnglishResult,
                                    sourceLanguage: 'Nepali',
                                    targetLanguage: 'English',
                                  );
                                },
                              ),
                            ],
                          ),
                        ],
                      ),
                    ),
                  ],
                ],
              ),
            ),
          ),

          // Favorites Tab
          _favorites.isEmpty
              ? Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Icon(
                      Icons.favorite_border,
                      size: 64,
                      color: isDarkMode ? Colors.teal.shade300 : Colors.teal,
                    ),
                    const SizedBox(height: 16),
                    Text(
                      'No favorites yet',
                      style: GoogleFonts.poppins(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 8),
                    Text(
                      'Translate words and add them to favorites',
                      style: GoogleFonts.poppins(
                        fontSize: 14,
                        color: Theme.of(context).textTheme.bodySmall?.color,
                      ),
                      textAlign: TextAlign.center,
                    ),
                  ],
                ),
              )
              : ListView.builder(
                physics: const BouncingScrollPhysics(),
                padding: const EdgeInsets.all(16),
                itemCount: _favorites.length,
                itemBuilder: (context, index) {
                  final favorite = _favorites[index];
                  final isEnToNe = favorite['sourceLanguage'] == 'English';
                  final color = isEnToNe ? Colors.teal : Colors.blue;

                  return Card(
                    elevation: isDarkMode ? 4 : 2,
                    margin: const EdgeInsets.only(bottom: 16),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(12),
                      side:
                          isDarkMode
                              ? BorderSide(color: color.withAlpha(60), width: 1)
                              : BorderSide.none,
                    ),
                    child: Padding(
                      padding: const EdgeInsets.all(16),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          // Source text
                          Text(
                            favorite['source'],
                            style: GoogleFonts.poppins(
                              fontSize: 16,
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                          const SizedBox(height: 8),

                          // Direction
                          Row(
                            children: [
                              Text(
                                favorite['sourceLanguage'],
                                style: GoogleFonts.poppins(
                                  fontSize: 12,
                                  color: color,
                                ),
                              ),
                              const SizedBox(width: 8),
                              Icon(Icons.arrow_forward, size: 12, color: color),
                              const SizedBox(width: 8),
                              Text(
                                favorite['targetLanguage'],
                                style: GoogleFonts.poppins(
                                  fontSize: 12,
                                  color: color,
                                ),
                              ),
                            ],
                          ),
                          const SizedBox(height: 8),

                          // Translated text
                          Container(
                            width: double.infinity,
                            padding: const EdgeInsets.all(12),
                            decoration: BoxDecoration(
                              color:
                                  isDarkMode
                                      ? color.withAlpha(30)
                                      : color.withAlpha(15),
                              borderRadius: BorderRadius.circular(8),
                            ),
                            child: Text(
                              favorite['translated'],
                              style: GoogleFonts.poppins(
                                fontSize: 16,
                                fontWeight: FontWeight.w500,
                              ),
                            ),
                          ),

                          // Actions
                          Row(
                            mainAxisAlignment: MainAxisAlignment.end,
                            children: [
                              // Remove from favorites
                              IconButton(
                                icon: const Icon(Icons.delete_outline),
                                color: Colors.red,
                                onPressed: () => _removeFromFavorites(index),
                              ),
                            ],
                          ),
                        ],
                      ),
                    ),
                  );
                },
              ),
        ],
      ),
    );
  }
}
