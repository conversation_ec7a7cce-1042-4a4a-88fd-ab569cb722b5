import 'package:url_launcher/url_launcher.dart';
import 'package:flutter/material.dart';

class UrlLauncherUtil {
  static Future<void> launchURL(String url) async {
    try {
      final Uri uri = Uri.parse(url);
      if (await canLaunchUrl(uri)) {
        await launchUrl(uri, mode: LaunchMode.externalApplication);
      } else {
        throw 'Could not launch $url';
      }
    } catch (e) {
      debugPrint('Error launching URL: $e');
    }
  }

  static Future<void> launchSEEResult() async {
    await launchURL('http://see.gov.np/exam/results');
  }

  static Future<void> launchHSEBResult() async {
    await launchURL('https://www.neb.gov.np/results');
  }

  static Future<void> launchTUResult() async {
    await launchURL('https://result.tuexam.edu.np/');
  }

  static Future<void> launchKUResult() async {
    await launchURL('https://examresults.ku.edu.np:81/');
  }
}
