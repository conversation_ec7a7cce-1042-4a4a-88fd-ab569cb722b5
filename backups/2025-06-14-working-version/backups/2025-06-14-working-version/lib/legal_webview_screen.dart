import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:webview_flutter/webview_flutter.dart';

/// A specialized WebView screen for displaying legal content like
/// Terms and Conditions or Privacy Policy with a cleaner interface.
/// Opens immediately without any loading countdown.
class LegalWebViewScreen extends StatefulWidget {
  final String url;
  final String title;

  const LegalWebViewScreen({super.key, required this.url, required this.title});

  @override
  State<LegalWebViewScreen> createState() => _LegalWebViewScreenState();
}

class _LegalWebViewScreenState extends State<LegalWebViewScreen> {
  late WebViewController _webViewController;
  final GlobalKey webViewKey = GlobalKey();

  bool _hasError = false;
  String _errorMessage = '';
  bool _webViewInitialized = false;

  @override
  void initState() {
    super.initState();
    // Initialize the controller without theme-dependent properties
    _webViewController =
        WebViewController()..setJavaScriptMode(JavaScriptMode.unrestricted);
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    // Complete the initialization here where it's safe to access Theme
    if (!_webViewInitialized) {
      _initWebView();
      _webViewInitialized = true;
    }
  }

  void _initWebView() {
    // Now it's safe to use Theme.of(context)
    _webViewController
      ..setBackgroundColor(Theme.of(context).scaffoldBackgroundColor)
      ..setNavigationDelegate(
        NavigationDelegate(
          onWebResourceError: (WebResourceError error) {
            setState(() {
              _hasError = true;
              _errorMessage = "Error: ${error.description}";
            });
          },
          onNavigationRequest: (NavigationRequest request) {
            // Allow navigation within the same domain
            final Uri requestUri = Uri.parse(request.url);
            final Uri initialUri = Uri.parse(widget.url);

            if (requestUri.host == initialUri.host) {
              return NavigationDecision.navigate;
            }

            // Prevent navigation to external sites
            return NavigationDecision.prevent;
          },
        ),
      )
      ..loadRequest(Uri.parse(widget.url));
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(
          widget.title,
          style: GoogleFonts.poppins(fontWeight: FontWeight.bold, fontSize: 18),
        ),
        centerTitle: true,
        elevation: 0,
        // No actions to keep the interface clean
      ),
      body: Stack(
        children: [
          // WebView - shows immediately without loading screen
          WebViewWidget(controller: _webViewController),

          // Error view - only shown if there's an error
          if (_hasError) _buildErrorView(),
        ],
      ),
    );
  }

  Widget _buildErrorView() {
    return Container(
      color: Theme.of(context).scaffoldBackgroundColor,
      child: Center(
        child: Padding(
          padding: const EdgeInsets.all(24.0),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Container(
                width: 100,
                height: 100,
                decoration: BoxDecoration(
                  color: Colors.red.withAlpha(25),
                  shape: BoxShape.circle,
                ),
                child: const Icon(
                  Icons.error_outline,
                  color: Colors.red,
                  size: 60,
                ),
              ),
              const SizedBox(height: 24),
              Text(
                'Unable to load document',
                style: GoogleFonts.poppins(
                  fontSize: 22,
                  fontWeight: FontWeight.bold,
                ),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 12),
              Text(
                _errorMessage,
                textAlign: TextAlign.center,
                style: GoogleFonts.poppins(fontSize: 14, color: Colors.red),
              ),
              const SizedBox(height: 32),
              SizedBox(
                width: double.infinity,
                child: ElevatedButton.icon(
                  icon: const Icon(Icons.refresh),
                  label: Text('Try Again', style: GoogleFonts.poppins()),
                  style: ElevatedButton.styleFrom(
                    padding: const EdgeInsets.symmetric(vertical: 12),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(12),
                    ),
                  ),
                  onPressed: () {
                    setState(() {
                      _hasError = false;
                    });
                    _webViewController.reload();
                  },
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
