import 'dart:io';
import 'dart:ui' as ui;
import 'package:flutter/material.dart';
import 'package:flutter/rendering.dart';
import 'package:flutter/services.dart';
import 'dart:typed_data';

// This is a simple tool to generate a student icon image for the splash screen
// Run this file with: flutter run -d macos lib/tools/create_student_icon.dart
// (or another platform of your choice)

void main() {
  runApp(const GenerateIconApp());
}

class GenerateIconApp extends StatelessWidget {
  const GenerateIconApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: 'Generate Student Icon',
      theme: ThemeData(
        colorScheme: ColorScheme.fromSeed(seedColor: Colors.blue),
        useMaterial3: true,
      ),
      home: const GenerateIconPage(),
    );
  }
}

class GenerateIconPage extends StatefulWidget {
  const GenerateIconPage({super.key});

  @override
  State<GenerateIconPage> createState() => _GenerateIconPageState();
}

class _GenerateIconPageState extends State<GenerateIconPage> {
  final GlobalKey _globalKey = GlobalKey();
  bool _isSaving = false;
  String _message = '';

  Future<void> _captureAndSavePng() async {
    setState(() {
      _isSaving = true;
      _message = 'Generating image...';
    });

    try {
      // Capture the widget as an image
      RenderRepaintBoundary boundary = _globalKey.currentContext!.findRenderObject() as RenderRepaintBoundary;
      ui.Image image = await boundary.toImage(pixelRatio: 3.0);
      ByteData? byteData = await image.toByteData(format: ui.ImageByteFormat.png);
      
      if (byteData != null) {
        Uint8List pngBytes = byteData.buffer.asUint8List();
        
        // Save the image to the Android resources directory
        final androidResDir = Directory('/Users/<USER>/Documents/augment-projects/Nepali Results/nepali_results/android/app/src/main/res');
        
        // Create directories for different densities
        final directories = [
          'drawable-hdpi',
          'drawable-mdpi',
          'drawable-xhdpi',
          'drawable-xxhdpi',
          'drawable-xxxhdpi',
          'drawable-night-hdpi',
          'drawable-night-mdpi',
          'drawable-night-xhdpi',
          'drawable-night-xxhdpi',
          'drawable-night-xxxhdpi',
        ];
        
        for (final dir in directories) {
          final directory = Directory('${androidResDir.path}/$dir');
          if (await directory.exists()) {
            final file = File('${directory.path}/android12splash.png');
            await file.writeAsBytes(pngBytes);
          }
        }
        
        setState(() {
          _message = 'Images saved to Android resources directories';
        });
      } else {
        setState(() {
          _message = 'Failed to generate image: ByteData is null';
        });
      }
    } catch (e) {
      setState(() {
        _message = 'Error: $e';
      });
    } finally {
      setState(() {
        _isSaving = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Generate Student Icon'),
      ),
      body: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            // This is the widget that will be captured as an image
            RepaintBoundary(
              key: _globalKey,
              child: Container(
                width: 512,
                height: 512,
                color: Colors.transparent,
                child: const Center(
                  child: Icon(
                    Icons.school,
                    size: 256,
                    color: Colors.blue,
                  ),
                ),
              ),
            ),
            const SizedBox(height: 20),
            ElevatedButton(
              onPressed: _isSaving ? null : _captureAndSavePng,
              child: Text(_isSaving ? 'Generating...' : 'Generate Icon'),
            ),
            const SizedBox(height: 20),
            Text(_message),
          ],
        ),
      ),
    );
  }
}
