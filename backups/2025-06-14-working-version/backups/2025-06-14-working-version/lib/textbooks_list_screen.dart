import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import '../models/textbook.dart';
import 'pdf_viewer_screen.dart';

class TextbooksListScreen extends StatelessWidget {
  final TextbookClass textbookClass;

  const TextbooksListScreen({super.key, required this.textbookClass});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(
          textbookClass.className,
          style: GoogleFonts.poppins(fontWeight: FontWeight.bold),
        ),
      ),
      body: Container(
        decoration: BoxDecoration(
          // Subtle background pattern
          color:
              Theme.of(context).brightness == Brightness.dark
                  ? const Color(0xFF1E1E1E)
                  : const Color(0xFFF5F5F5),
        ),
        child: ListView.builder(
          padding: const EdgeInsets.all(16.0),
          itemCount: textbookClass.textbooks.length,
          itemBuilder: (context, index) {
            final textbook = textbookClass.textbooks[index];
            return _buildTextbookCard(context, textbook);
          },
        ),
      ),
    );
  }

  Widget _buildTextbookCard(BuildContext context, Textbook textbook) {
    // Generate a color based on the textbook title
    final Color cardColor = _getSubjectColor(textbook.title, context);

    return Card(
      margin: const EdgeInsets.only(bottom: 16.0),
      elevation: 4,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: InkWell(
        borderRadius: BorderRadius.circular(12),
        onTap: () {
          Navigator.push(
            context,
            MaterialPageRoute(
              builder:
                  (context) => PdfViewerScreen(
                    title: textbook.title,
                    pdfUrl: textbook.pdfUrl,
                  ),
            ),
          );
        },
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Colored header
            Container(
              width: double.infinity,
              padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 16),
              decoration: BoxDecoration(
                color: cardColor.withAlpha(204), // 0.8 * 255 = 204
                borderRadius: const BorderRadius.only(
                  topLeft: Radius.circular(12),
                  topRight: Radius.circular(12),
                ),
              ),
              child: Row(
                children: [
                  Icon(
                    _getSubjectIcon(textbook.title),
                    color: Colors.white,
                    size: 24,
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: Text(
                      textbook.title,
                      style: GoogleFonts.poppins(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                        color: Colors.white,
                      ),
                    ),
                  ),
                ],
              ),
            ),

            // Content
            Padding(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  if (textbook.description != null) ...[
                    Text(
                      textbook.description!,
                      style: GoogleFonts.poppins(
                        fontSize: 14,
                        color:
                            Theme.of(context).brightness == Brightness.dark
                                ? Colors.white70
                                : Colors.black87,
                      ),
                    ),
                    const SizedBox(height: 16),
                  ],

                  // View PDF button
                  SizedBox(
                    width: double.infinity,
                    child: ElevatedButton.icon(
                      icon: const Icon(Icons.picture_as_pdf),
                      label: const Text('View PDF'),
                      style: ElevatedButton.styleFrom(
                        backgroundColor: cardColor,
                        foregroundColor: Colors.white,
                        padding: const EdgeInsets.symmetric(vertical: 12),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(8),
                        ),
                      ),
                      onPressed: () {
                        Navigator.push(
                          context,
                          MaterialPageRoute(
                            builder:
                                (context) => PdfViewerScreen(
                                  title: textbook.title,
                                  pdfUrl: textbook.pdfUrl,
                                ),
                          ),
                        );
                      },
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Color _getSubjectColor(String subject, BuildContext context) {
    final String lowerSubject = subject.toLowerCase();

    if (lowerSubject.contains('english')) {
      return const Color(0xFF1565C0); // Blue
    } else if (lowerSubject.contains('math')) {
      return const Color(0xFF00897B); // Teal
    } else if (lowerSubject.contains('science') ||
        lowerSubject.contains('physics') ||
        lowerSubject.contains('chemistry') ||
        lowerSubject.contains('biology')) {
      return const Color(0xFF7B1FA2); // Purple
    } else if (lowerSubject.contains('social')) {
      return const Color(0xFFEF6C00); // Orange
    } else if (lowerSubject.contains('computer')) {
      return const Color(0xFF0277BD); // Light Blue
    } else if (lowerSubject.contains('nepali')) {
      return const Color(0xFFC62828); // Red
    } else {
      return Theme.of(context).colorScheme.primary;
    }
  }

  IconData _getSubjectIcon(String subject) {
    final String lowerSubject = subject.toLowerCase();

    if (lowerSubject.contains('english')) {
      return Icons.language;
    } else if (lowerSubject.contains('math')) {
      return Icons.calculate;
    } else if (lowerSubject.contains('science')) {
      return Icons.science;
    } else if (lowerSubject.contains('physics')) {
      return Icons.bolt;
    } else if (lowerSubject.contains('chemistry')) {
      return Icons.science;
    } else if (lowerSubject.contains('biology')) {
      return Icons.biotech;
    } else if (lowerSubject.contains('social')) {
      return Icons.public;
    } else if (lowerSubject.contains('computer')) {
      return Icons.computer;
    } else if (lowerSubject.contains('nepali')) {
      return Icons.menu_book;
    } else {
      return Icons.book;
    }
  }
}
