import 'dart:async';
import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:webview_flutter/webview_flutter.dart';
import 'package:url_launcher/url_launcher.dart';

class WebViewScreen extends StatefulWidget {
  final String url;
  final String title;

  const WebViewScreen({super.key, required this.url, required this.title});

  @override
  State<WebViewScreen> createState() => _WebViewScreenState();
}

class _WebViewScreenState extends State<WebViewScreen> {
  late WebViewController _webViewController;
  final GlobalKey webViewKey = GlobalKey();

  bool _isLoading = true;
  double _loadingProgress = 0.0;
  bool _hasError = false;
  String _errorMessage = '';
  String _currentUrl = '';

  // Timeout handling
  Timer? _loadingTimer;
  bool _isTimedOut = false;

  @override
  void initState() {
    super.initState();
    _currentUrl = widget.url;
    _initWebView();

    // Start a timer to check if the page loads within 10 seconds
    _loadingTimer = Timer(const Duration(seconds: 10), () {
      if (_isLoading && mounted) {
        setState(() {
          _isTimedOut = true;
        });
      }
    });
  }

  void _initWebView() {
    _webViewController = WebViewController()
      ..setJavaScriptMode(JavaScriptMode.unrestricted)
      ..setNavigationDelegate(
        NavigationDelegate(
          onPageStarted: (String url) {
            setState(() {
              _isLoading = true;
              _hasError = false;
              _currentUrl = url;
            });
          },
          onProgress: (int progress) {
            setState(() {
              _loadingProgress = progress / 100;
            });
          },
          onPageFinished: (String url) {
            setState(() {
              _isLoading = false;
              _isTimedOut = false;
            });
            _loadingTimer?.cancel();
          },
          onWebResourceError: (WebResourceError error) {
            setState(() {
              _hasError = true;
              _errorMessage = "Error: ${error.description}";
              _isLoading = false;
            });
            _loadingTimer?.cancel();
          },
          onNavigationRequest: (NavigationRequest request) {
            final url = request.url;
            
            // Handle PDF files or other special URLs if needed
            if (url.endsWith('.pdf')) {
              _openInExternalBrowser();
              return NavigationDecision.prevent;
            }
            
            return NavigationDecision.navigate;
          },
        ),
      )
      ..loadRequest(Uri.parse(_currentUrl));
  }

  @override
  void dispose() {
    _loadingTimer?.cancel();
    super.dispose();
  }

  void _tryAlternateUrl() {
    // Try to load a more reliable URL
    if (_currentUrl.contains('neb.gov.np')) {
      _currentUrl = 'https://www.hamropatro.com/education';
    } else if (_currentUrl.contains('tuexam.edu.np')) {
      _currentUrl = 'https://www.hamropatro.com/education';
    } else if (_currentUrl.contains('examresults.ku.edu.np')) {
      _currentUrl = 'https://www.hamropatro.com/education';
    } else if (_currentUrl.contains('puexam.edu.np')) {
      _currentUrl = 'https://www.hamropatro.com/education';
    } else if (_currentUrl.contains('itms.ctevt.org.np')) {
      _currentUrl = 'https://www.hamropatro.com/education';
    } else if (_currentUrl.contains('dotm.gov.np')) {
      _currentUrl = 'https://www.hamropatro.com/license';
    } else if (_currentUrl.contains('dvprogram.state.gov')) {
      _currentUrl = 'https://www.dvlottery.state.gov/';
    } else {
      // Default fallback
      _currentUrl =
          'https://www.google.com/search?q=${Uri.encodeComponent(widget.title)}';
    }

    setState(() {
      _isLoading = true;
      _hasError = false;
      _isTimedOut = false;
    });

    // Reset the timeout timer
    _loadingTimer?.cancel();
    _loadingTimer = Timer(const Duration(seconds: 10), () {
      if (_isLoading && mounted) {
        setState(() {
          _isTimedOut = true;
        });
      }
    });

    _webViewController.loadRequest(Uri.parse(_currentUrl));
  }

  Future<void> _openInExternalBrowser() async {
    final Uri url = Uri.parse(_currentUrl);
    try {
      await launchUrl(url, mode: LaunchMode.externalApplication);
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Could not launch $url'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  void _loadGoogleSearch() {
    final searchQuery = '${widget.title} results nepal';
    final googleUrl =
        'https://www.google.com/search?q=${Uri.encodeComponent(searchQuery)}';

    setState(() {
      _currentUrl = googleUrl;
      _isLoading = true;
      _hasError = false;
      _isTimedOut = false;
    });

    // Reset the timeout timer
    _loadingTimer?.cancel();
    _loadingTimer = Timer(const Duration(seconds: 10), () {
      if (_isLoading && mounted) {
        setState(() {
          _isTimedOut = true;
        });
      }
    });

    _webViewController.loadRequest(Uri.parse(googleUrl));
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(widget.title),
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: () {
              setState(() {
                _isLoading = true;
                _hasError = false;
                _isTimedOut = false;
              });

              // Reset the timeout timer
              _loadingTimer?.cancel();
              _loadingTimer = Timer(const Duration(seconds: 10), () {
                if (_isLoading && mounted) {
                  setState(() {
                    _isTimedOut = true;
                  });
                }
              });

              _webViewController.reload();
            },
          ),
          IconButton(
            icon: const Icon(Icons.open_in_browser),
            onPressed: _openInExternalBrowser,
          ),
          PopupMenuButton<String>(
            onSelected: (value) {
              if (value == 'alternate') {
                _tryAlternateUrl();
              } else if (value == 'google') {
                _loadGoogleSearch();
              }
            },
            itemBuilder:
                (context) => [
                  const PopupMenuItem(
                    value: 'alternate',
                    child: Text('Try Alternate Site'),
                  ),
                  const PopupMenuItem(
                    value: 'google',
                    child: Text('Search on Google'),
                  ),
                ],
          ),
        ],
      ),
      body: Stack(
        children: [
          WebViewWidget(controller: _webViewController),

          // Loading indicator
          if (_isLoading && !_isTimedOut)
            Container(
              color: Theme.of(context).scaffoldBackgroundColor,
              child: Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    // Animated loading indicator
                    SizedBox(
                      width: 120,
                      height: 120,
                      child: Stack(
                        alignment: Alignment.center,
                        children: [
                          CircularProgressIndicator(
                            value:
                                _loadingProgress > 0 ? _loadingProgress : null,
                            strokeWidth: 3,
                            valueColor: AlwaysStoppedAnimation<Color>(
                              Theme.of(context).colorScheme.primary,
                            ),
                          ),
                          // Inner circle with percentage
                          if (_loadingProgress > 0)
                            Text(
                              '${(_loadingProgress * 100).toInt()}%',
                              style: GoogleFonts.poppins(
                                fontSize: 16,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                        ],
                      ),
                    ),
                    const SizedBox(height: 24),
                    Text(
                      'Loading ${widget.title}...',
                      style: GoogleFonts.poppins(
                        fontSize: 16,
                        fontWeight: FontWeight.w500,
                      ),
                      textAlign: TextAlign.center,
                    ),
                    const SizedBox(height: 8),
                    Text(
                      'Please wait while we connect to the server',
                      style: GoogleFonts.poppins(
                        fontSize: 12,
                        color: Colors.grey,
                      ),
                      textAlign: TextAlign.center,
                    ),
                  ],
                ),
              ),
            ),

          // Error and timeout views would go here (similar to the original)
          // Timeout message
          if (_isTimedOut && _isLoading)
            _buildTimeoutMessage(),

          // Error view
          if (_hasError)
            _buildErrorView(),
        ],
      ),
    );
  }

  Widget _buildTimeoutMessage() {
    return Container(
      color: Theme.of(context).scaffoldBackgroundColor,
      child: Center(
        child: Padding(
          padding: const EdgeInsets.all(24.0),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Container(
                width: 100,
                height: 100,
                decoration: BoxDecoration(
                  color: Colors.orange.withAlpha(30),
                  shape: BoxShape.circle,
                ),
                child: const Icon(
                  Icons.timer_outlined,
                  color: Colors.orange,
                  size: 60,
                ),
              ),
              const SizedBox(height: 24),
              Text(
                'Taking longer than expected',
                style: GoogleFonts.poppins(
                  fontSize: 22,
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 12),
              Text(
                'This website is taking a long time to load. You can continue waiting or try one of the options below.',
                textAlign: TextAlign.center,
                style: GoogleFonts.poppins(fontSize: 14),
              ),
              const SizedBox(height: 32),
              SizedBox(
                width: double.infinity,
                child: ElevatedButton.icon(
                  icon: const Icon(Icons.open_in_browser),
                  label: Text(
                    'Open in External Browser',
                    style: GoogleFonts.poppins(),
                  ),
                  style: ElevatedButton.styleFrom(
                    padding: const EdgeInsets.symmetric(vertical: 12),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(12),
                    ),
                  ),
                  onPressed: _openInExternalBrowser,
                ),
              ),
              const SizedBox(height: 16),
              SizedBox(
                width: double.infinity,
                child: OutlinedButton.icon(
                  icon: const Icon(Icons.swap_horiz),
                  label: Text(
                    'Try Alternate Site',
                    style: GoogleFonts.poppins(),
                  ),
                  style: OutlinedButton.styleFrom(
                    padding: const EdgeInsets.symmetric(vertical: 12),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(12),
                    ),
                  ),
                  onPressed: _tryAlternateUrl,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildErrorView() {
    return Container(
      color: Theme.of(context).scaffoldBackgroundColor,
      child: Center(
        child: Padding(
          padding: const EdgeInsets.all(24.0),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Container(
                width: 100,
                height: 100,
                decoration: BoxDecoration(
                  color: Colors.red.withAlpha(30),
                  shape: BoxShape.circle,
                ),
                child: const Icon(
                  Icons.error_outline,
                  color: Colors.red,
                  size: 60,
                ),
              ),
              const SizedBox(height: 24),
              Text(
                'This website cannot be loaded inside the app',
                style: GoogleFonts.poppins(
                  fontSize: 22,
                  fontWeight: FontWeight.bold,
                ),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 12),
              Text(
                _errorMessage,
                textAlign: TextAlign.center,
                style: GoogleFonts.poppins(
                  fontSize: 14,
                  color: Colors.red,
                ),
              ),
              const SizedBox(height: 32),
              SizedBox(
                width: double.infinity,
                child: ElevatedButton.icon(
                  icon: const Icon(Icons.open_in_browser),
                  label: Text(
                    'Open in External Browser',
                    style: GoogleFonts.poppins(),
                  ),
                  style: ElevatedButton.styleFrom(
                    padding: const EdgeInsets.symmetric(vertical: 12),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(12),
                    ),
                  ),
                  onPressed: _openInExternalBrowser,
                ),
              ),
              const SizedBox(height: 16),
              SizedBox(
                width: double.infinity,
                child: OutlinedButton.icon(
                  icon: const Icon(Icons.swap_horiz),
                  label: Text(
                    'Try Alternate Site',
                    style: GoogleFonts.poppins(),
                  ),
                  style: OutlinedButton.styleFrom(
                    padding: const EdgeInsets.symmetric(vertical: 12),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(12),
                    ),
                  ),
                  onPressed: _tryAlternateUrl,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
