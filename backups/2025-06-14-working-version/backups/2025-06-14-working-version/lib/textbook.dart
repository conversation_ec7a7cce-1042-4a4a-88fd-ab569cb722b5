class Textbook {
  final String title;
  final String pdfUrl;
  final String? description;
  final String? coverImageUrl;

  Textbook({
    required this.title,
    required this.pdfUrl,
    this.description,
    this.coverImageUrl,
  });
}

class TextbookMedium {
  final String mediumName;
  final List<Textbook> textbooks;

  TextbookMedium({required this.mediumName, required this.textbooks});
}

class TextbookClass {
  final String className;
  final List<TextbookMedium> mediums;

  TextbookClass({required this.className, required this.mediums});
}
