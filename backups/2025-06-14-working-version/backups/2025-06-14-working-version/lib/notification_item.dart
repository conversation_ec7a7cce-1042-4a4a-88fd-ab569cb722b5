import 'dart:convert';

/// Model class for a notification item
class NotificationItem {
  /// Unique identifier for the notification
  final String id;

  /// Title of the notification
  final String title;

  /// Body/message of the notification
  final String body;

  /// Timestamp when the notification was received
  final DateTime timestamp;

  /// Additional data associated with the notification
  final Map<String, dynamic>? data;

  /// Whether the notification has been read
  bool isRead;

  /// Constructor
  NotificationItem({
    required this.id,
    required this.title,
    required this.body,
    required this.timestamp,
    this.data,
    this.isRead = false,
  });

  /// Create a notification from a JSON map
  factory NotificationItem.fromJson(Map<String, dynamic> json) {
    return NotificationItem(
      id: json['id'] as String,
      title: json['title'] as String,
      body: json['body'] as String,
      timestamp: DateTime.parse(json['timestamp'] as String),
      data: json['data'] != null
          ? jsonDecode(json['data'] as String) as Map<String, dynamic>
          : null,
      isRead: json['isRead'] as bool? ?? false,
    );
  }

  /// Convert notification to a JSON map
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'title': title,
      'body': body,
      'timestamp': timestamp.toIso8601String(),
      'data': data != null ? jsonEncode(data) : null,
      'isRead': isRead,
    };
  }

  /// Check if the notification is older than the specified duration
  bool isOlderThan(Duration duration) {
    final now = DateTime.now();
    return now.difference(timestamp) > duration;
  }

  /// Mark the notification as read
  void markAsRead() {
    isRead = true;
  }

  @override
  String toString() {
    return 'NotificationItem{id: $id, title: $title, body: $body, timestamp: $timestamp, isRead: $isRead}';
  }
}
