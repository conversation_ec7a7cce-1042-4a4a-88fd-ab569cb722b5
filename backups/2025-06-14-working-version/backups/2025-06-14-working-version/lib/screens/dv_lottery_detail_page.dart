import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import '../services/chrome_custom_tabs_service.dart';

class DVLotteryDetailPage extends StatefulWidget {
  const DVLotteryDetailPage({super.key});

  @override
  State<DVLotteryDetailPage> createState() => _DVLotteryDetailPageState();
}

class _DVLotteryDetailPageState extends State<DVLotteryDetailPage> {
  Future<void> _launchChromeCustomTab(String url, String title) async {
    try {
      if (url.contains('dvprogram.state.gov')) {
        await ChromeCustomTabsService.launchDVLottery();
      } else {
        await ChromeCustomTabsService.launch(url, title: title);
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Could not open $title: ${e.toString()}')),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('DV Lottery'),
        backgroundColor: Colors.red.shade700,
        foregroundColor: Colors.white,
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header with DV Lottery logo
            Card(
              elevation: 4,
              child: Padding(
                padding: const EdgeInsets.all(20.0),
                child: Row(
                  children: [
                    Container(
                      width: 60,
                      height: 60,
                      decoration: BoxDecoration(
                        color: Colors.red.shade100,
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: Icon(
                        Icons.card_giftcard,
                        size: 40,
                        color: Colors.red.shade700,
                      ),
                    ),
                    const SizedBox(width: 16),
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            'DV Lottery',
                            style: GoogleFonts.poppins(
                              fontSize: 20,
                              fontWeight: FontWeight.bold,
                              color: Colors.red.shade700,
                            ),
                          ),
                          const SizedBox(height: 4),
                          Text(
                            'Diversity Visa Program',
                            style: GoogleFonts.poppins(
                              fontSize: 14,
                              color: Colors.grey.shade600,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
            ),

            const SizedBox(height: 24),

            // Official Sites Section
            _buildSectionTitle('Official Sites', Icons.public),
            const SizedBox(height: 16),
            _buildWebsiteCard(
              'DV Lottery Official Website',
              'https://dvprogram.state.gov',
              Icons.card_giftcard,
              Colors.red,
              () => ChromeCustomTabsService.launchDVLottery(),
            ),

            const SizedBox(height: 32),

            // Information Section
            _buildSectionTitle('Information', Icons.info_outline),
            const SizedBox(height: 16),
            Card(
              elevation: 2,
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'About DV Lottery',
                      style: GoogleFonts.poppins(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                        color: Colors.red.shade700,
                      ),
                    ),
                    const SizedBox(height: 12),
                    Text(
                      '• The Diversity Visa Program makes up to 50,000 immigrant visas available annually\n'
                      '• Results are published on the official U.S. State Department website\n'
                      '• Check your results using your confirmation number\n'
                      '• Application period is typically from October to November\n'
                      '• Results are usually announced in May of the following year',
                      style: GoogleFonts.poppins(
                        fontSize: 14,
                        color: Colors.grey.shade700,
                      ),
                    ),
                  ],
                ),
              ),
            ),

            const SizedBox(height: 24),

            // Important Notice Section
            _buildSectionTitle('Important Notice', Icons.warning),
            const SizedBox(height: 16),
            Card(
              elevation: 2,
              color: Colors.amber.shade50,
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        Icon(Icons.warning, color: Colors.amber.shade700),
                        const SizedBox(width: 8),
                        Text(
                          'Beware of Scams',
                          style: GoogleFonts.poppins(
                            fontSize: 16,
                            fontWeight: FontWeight.bold,
                            color: Colors.amber.shade700,
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 12),
                    Text(
                      '• Only use the official U.S. State Department website\n'
                      '• The DV program is completely free to enter\n'
                      '• Never pay money to check results or apply\n'
                      '• Be cautious of fraudulent websites and emails',
                      style: GoogleFonts.poppins(
                        fontSize: 14,
                        color: Colors.amber.shade800,
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSectionTitle(String title, IconData icon) {
    return Row(
      children: [
        Icon(icon, color: Colors.red.shade700, size: 24),
        const SizedBox(width: 8),
        Text(
          title,
          style: GoogleFonts.poppins(
            fontSize: 18,
            fontWeight: FontWeight.bold,
            color: Colors.red.shade700,
          ),
        ),
      ],
    );
  }

  Widget _buildWebsiteCard(String title, String url, IconData icon, Color color, [VoidCallback? customLaunch]) {
    return Card(
      elevation: 2,
      child: InkWell(
        onTap: () async {
          try {
            if (customLaunch != null) {
              customLaunch();
            } else {
              await _launchChromeCustomTab(url, title);
            }
          } catch (e) {
            if (mounted) {
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(content: Text('Could not open $title: ${e.toString()}')),
              );
            }
          }
        },
        borderRadius: BorderRadius.circular(12),
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Row(
            children: [
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: color.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Icon(icon, color: color, size: 24),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      title,
                      style: GoogleFonts.poppins(
                        fontSize: 16,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      'Tap to open in browser',
                      style: GoogleFonts.poppins(
                        fontSize: 12,
                        color: Colors.grey.shade600,
                      ),
                    ),
                  ],
                ),
              ),
              const Icon(Icons.arrow_forward_ios, size: 16),
            ],
          ),
        ),
      ),
    );
  }
}
