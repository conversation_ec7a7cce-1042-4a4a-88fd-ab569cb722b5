import 'package:flutter/material.dart';
import 'package:url_launcher/url_launcher.dart';
import 'package:google_fonts/google_fonts.dart';
import '../services/chrome_custom_tabs_service.dart';

class HSEBResultsDetailPage extends StatefulWidget {
  const HSEBResultsDetailPage({super.key});

  @override
  State<HSEBResultsDetailPage> createState() => _HSEBResultsDetailPageState();
}

class _HSEBResultsDetailPageState extends State<HSEBResultsDetailPage> {
  final TextEditingController _symbolController = TextEditingController();

  @override
  void dispose() {
    _symbolController.dispose();
    super.dispose();
  }

  Future<void> _launchChromeCustomTab(String url, String title) async {
    try {
      if (url.contains('ekantipur.com')) {
        await ChromeCustomTabsService.launchEKantipurResults(url, title);
      } else if (url.contains('ntc.net.np')) {
        await ChromeCustomTabsService.launchNTCPortal(url, title);
      } else if (url.contains('neb.gov.np')) {
        await ChromeCustomTabsService.launchHSEBResults();
      } else {
        await ChromeCustomTabsService.launch(url, title: title);
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Could not open $title: ${e.toString()}')),
        );
      }
    }
  }

  Future<void> _launchSMS(String message, String number) async {
    final Uri smsUri = Uri(
      scheme: 'sms',
      path: number,
      queryParameters: {'body': message},
    );
    
    if (await canLaunchUrl(smsUri)) {
      await launchUrl(smsUri);
    } else {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('Could not open SMS app')),
        );
      }
    }
  }

  Future<void> _launchPhone(String number) async {
    final Uri phoneUri = Uri(scheme: 'tel', path: number);
    
    if (await canLaunchUrl(phoneUri)) {
      await launchUrl(phoneUri);
    } else {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('Could not open phone app')),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('HSEB/NEB Results'),
        backgroundColor: Colors.indigo.shade700,
        foregroundColor: Colors.white,
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Official Sites Section
            _buildSectionTitle('Official Sites', Icons.public),
            const SizedBox(height: 16),
            _buildWebsiteCard(
              'NEB Official Website',
              'https://www.neb.gov.np/results',
              Icons.school,
              Colors.indigo,
            ),
            const SizedBox(height: 12),
            _buildWebsiteCard(
              'NTC NEB Portal',
              'https://neb.ntc.net.np/',
              Icons.language,
              Colors.blue,
            ),
            const SizedBox(height: 12),
            _buildWebsiteCard(
              'eKantipur NEB Results',
              'https://results.ekantipur.com/neb-results-with-marksheet.php',
              Icons.article,
              Colors.red,
            ),

            const SizedBox(height: 32),

            // SMS Result Check Section
            _buildSectionTitle('SMS Result Check', Icons.sms),
            const SizedBox(height: 16),
            _buildSMSSection(),

            const SizedBox(height: 32),

            // IVR Section
            _buildSectionTitle('Voice Call (IVR)', Icons.phone),
            const SizedBox(height: 16),
            _buildIVRSection(),
          ],
        ),
      ),
    );
  }

  Widget _buildSectionTitle(String title, IconData icon) {
    return Row(
      children: [
        Icon(icon, color: Colors.indigo.shade700, size: 24),
        const SizedBox(width: 8),
        Text(
          title,
          style: GoogleFonts.poppins(
            fontSize: 18,
            fontWeight: FontWeight.bold,
            color: Colors.indigo.shade700,
          ),
        ),
      ],
    );
  }

  Widget _buildWebsiteCard(String title, String url, IconData icon, Color color) {
    return Card(
      elevation: 2,
      child: InkWell(
        onTap: () => _launchChromeCustomTab(url, title),
        borderRadius: BorderRadius.circular(12),
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Row(
            children: [
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: color.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Icon(icon, color: color, size: 24),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      title,
                      style: GoogleFonts.poppins(
                        fontSize: 16,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      'Tap to open in browser',
                      style: GoogleFonts.poppins(
                        fontSize: 12,
                        color: Colors.grey.shade600,
                      ),
                    ),
                  ],
                ),
              ),
              const Icon(Icons.arrow_forward_ios, size: 16),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildSMSSection() {
    return Card(
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Format: NEB <Symbol Number>',
              style: GoogleFonts.poppins(
                fontSize: 16,
                fontWeight: FontWeight.w600,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              'Example: NEB 1234567',
              style: GoogleFonts.poppins(
                fontSize: 14,
                color: Colors.grey.shade600,
              ),
            ),
            const SizedBox(height: 16),
            TextField(
              controller: _symbolController,
              decoration: InputDecoration(
                labelText: 'Enter Symbol Number',
                hintText: 'e.g., 1234567',
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
                prefixIcon: const Icon(Icons.numbers),
              ),
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: ElevatedButton.icon(
                    onPressed: () {
                      if (_symbolController.text.isNotEmpty) {
                        _launchSMS('NEB ${_symbolController.text}', '1600');
                      } else {
                        ScaffoldMessenger.of(context).showSnackBar(
                          const SnackBar(content: Text('Please enter symbol number')),
                        );
                      }
                    },
                    icon: const Icon(Icons.sms),
                    label: const Text('SMS to 1600 (NTC)'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.green,
                      foregroundColor: Colors.white,
                      padding: const EdgeInsets.symmetric(vertical: 12),
                    ),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 8),
            Row(
              children: [
                Expanded(
                  child: ElevatedButton.icon(
                    onPressed: () {
                      if (_symbolController.text.isNotEmpty) {
                        _launchSMS('NEB ${_symbolController.text}', '35001');
                      } else {
                        ScaffoldMessenger.of(context).showSnackBar(
                          const SnackBar(content: Text('Please enter symbol number')),
                        );
                      }
                    },
                    icon: const Icon(Icons.sms),
                    label: const Text('SMS to 35001 (Ncell)'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.purple,
                      foregroundColor: Colors.white,
                      padding: const EdgeInsets.symmetric(vertical: 12),
                    ),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildIVRSection() {
    return Card(
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Call 1601 for Voice Results',
              style: GoogleFonts.poppins(
                fontSize: 16,
                fontWeight: FontWeight.w600,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              'Available for NTC users only',
              style: GoogleFonts.poppins(
                fontSize: 14,
                color: Colors.grey.shade600,
              ),
            ),
            const SizedBox(height: 16),
            SizedBox(
              width: double.infinity,
              child: ElevatedButton.icon(
                onPressed: () => _launchPhone('1601'),
                icon: const Icon(Icons.phone),
                label: const Text('Call 1601'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.indigo.shade700,
                  foregroundColor: Colors.white,
                  padding: const EdgeInsets.symmetric(vertical: 12),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}


