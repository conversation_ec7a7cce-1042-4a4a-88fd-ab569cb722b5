import 'package:flutter/foundation.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../models/notification_item.dart';

/// Simplified notification provider for the app
class NotificationProvider extends ChangeNotifier {
  // Notification settings
  bool _areNotificationsEnabled = true;
  
  // Notification count (for badge)
  int _notificationCount = 0;
  
  // List of notifications
  List<NotificationItem> _notifications = [];

  // Getters
  bool get areNotificationsEnabled => _areNotificationsEnabled;
  int get notificationCount => _notificationCount;
  List<NotificationItem> get notifications => _notifications;

  /// Initialize the notification provider
  Future<void> initialize() async {
    try {
      await _loadNotificationSettings();
      await _loadNotifications();
    } catch (e) {
      if (kDebugMode) {
        print('Error initializing notification provider: $e');
      }
    }
  }

  /// Load notification settings from shared preferences
  Future<void> _loadNotificationSettings() async {
    final prefs = await SharedPreferences.getInstance();
    _areNotificationsEnabled = prefs.getBool('notifications_enabled') ?? true;
    notifyListeners();
  }

  /// Save notification settings to shared preferences
  Future<void> _saveNotificationSettings() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setBool('notifications_enabled', _areNotificationsEnabled);
  }

  /// Load notifications from storage
  Future<void> _loadNotifications() async {
    // For now, just create some sample notifications
    _notifications = [
      NotificationItem(
        id: '1',
        title: 'Welcome to Nepali Results',
        message: 'Check your exam results easily with our app',
        timestamp: DateTime.now().subtract(const Duration(hours: 1)),
        isRead: false,
        category: 'general',
        url: '',
      ),
    ];
    _updateNotificationCount();
  }

  /// Toggle main notifications setting
  Future<void> toggleNotifications(bool value) async {
    _areNotificationsEnabled = value;
    await _saveNotificationSettings();
    notifyListeners();
  }

  /// Update the notification count
  void _updateNotificationCount() {
    _notificationCount = _notifications.where((n) => !n.isRead).length;
    notifyListeners();
  }

  /// Mark a notification as read
  void markNotificationAsRead(String id) {
    final index = _notifications.indexWhere((n) => n.id == id);
    if (index != -1) {
      _notifications[index] = _notifications[index].copyWith(isRead: true);
      _updateNotificationCount();
    }
  }

  /// Mark all notifications as read
  void markAllNotificationsAsRead() {
    for (int i = 0; i < _notifications.length; i++) {
      _notifications[i] = _notifications[i].copyWith(isRead: true);
    }
    _updateNotificationCount();
  }

  /// Clear all notifications
  Future<void> clearAllNotifications() async {
    _notifications.clear();
    _updateNotificationCount();
  }

  /// Clear notification count (e.g., when user views notifications)
  void clearNotificationCount() {
    if (_notificationCount > 0) {
      _notificationCount = 0;
      notifyListeners();
    }
  }

  /// Add a notification
  void addNotification(NotificationItem notification) {
    _notifications.insert(0, notification);
    _updateNotificationCount();
    notifyListeners();
  }

  /// Simulate receiving a new notification (for testing)
  Future<void> simulateNewNotification() async {
    final notification = NotificationItem(
      id: DateTime.now().millisecondsSinceEpoch.toString(),
      title: 'New Result Available',
      message: 'Check the latest exam results now!',
      timestamp: DateTime.now(),
      isRead: false,
      category: 'results',
      url: '',
    );
    addNotification(notification);
  }
}
