import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:nepali_utils/nepali_utils.dart';
import 'package:intl/intl.dart';

class NepaliDateConverter extends StatefulWidget {
  const NepaliDateConverter({super.key});

  @override
  State<NepaliDateConverter> createState() => _NepaliDateConverterState();
}

class _NepaliDateConverterState extends State<NepaliDateConverter> {
  // Controllers for BS date input
  final TextEditingController _bsYearController = TextEditingController();
  final TextEditingController _bsMonthController = TextEditingController();
  final TextEditingController _bsDayController = TextEditingController();
  
  // Controllers for AD date input
  final TextEditingController _adYearController = TextEditingController();
  final TextEditingController _adMonthController = TextEditingController();
  final TextEditingController _adDayController = TextEditingController();
  
  // Results
  String _bsToAdResult = '';
  String _adToBsResult = '';
  
  // Error messages
  String _bsError = '';
  String _adError = '';
  
  @override
  void initState() {
    super.initState();
    
    // Set current date as default
    final now = DateTime.now();
    final nepaliDate = NepaliDateTime.now();
    
    // Set AD date
    _adYearController.text = now.year.toString();
    _adMonthController.text = now.month.toString();
    _adDayController.text = now.day.toString();
    
    // Set BS date
    _bsYearController.text = nepaliDate.year.toString();
    _bsMonthController.text = nepaliDate.month.toString();
    _bsDayController.text = nepaliDate.day.toString();
  }
  
  @override
  void dispose() {
    _bsYearController.dispose();
    _bsMonthController.dispose();
    _bsDayController.dispose();
    _adYearController.dispose();
    _adMonthController.dispose();
    _adDayController.dispose();
    super.dispose();
  }
  
  // Convert BS to AD
  void _convertBsToAd() {
    setState(() {
      _bsError = '';
      _bsToAdResult = '';
      
      try {
        final year = int.parse(_bsYearController.text);
        final month = int.parse(_bsMonthController.text);
        final day = int.parse(_bsDayController.text);
        
        // Validate input
        if (year < 2000 || year > 2090) {
          _bsError = 'Year must be between 2000 and 2090 BS';
          return;
        }
        
        if (month < 1 || month > 12) {
          _bsError = 'Month must be between 1 and 12';
          return;
        }
        
        if (day < 1 || day > 32) {
          _bsError = 'Day must be between 1 and 32';
          return;
        }
        
        // Convert BS to AD
        final nepaliDate = NepaliDateTime(year, month, day);
        final adDate = nepaliDate.toDateTime();
        
        // Format the result
        final formatter = DateFormat('EEEE, MMMM d, yyyy');
        _bsToAdResult = formatter.format(adDate);
      } catch (e) {
        _bsError = 'Invalid date format';
      }
    });
  }
  
  // Convert AD to BS
  void _convertAdToBs() {
    setState(() {
      _adError = '';
      _adToBsResult = '';
      
      try {
        final year = int.parse(_adYearController.text);
        final month = int.parse(_adMonthController.text);
        final day = int.parse(_adDayController.text);
        
        // Validate input
        if (year < 1944 || year > 2033) {
          _adError = 'Year must be between 1944 and 2033 AD';
          return;
        }
        
        if (month < 1 || month > 12) {
          _adError = 'Month must be between 1 and 12';
          return;
        }
        
        if (day < 1 || day > 31) {
          _adError = 'Day must be between 1 and 31';
          return;
        }
        
        // Convert AD to BS
        final adDate = DateTime(year, month, day);
        final nepaliDate = NepaliDateTime.fromDateTime(adDate);
        
        // Format the result
        final nepaliMonthName = NepaliDateFormat.MMMM().format(nepaliDate);
        _adToBsResult = '${nepaliDate.day} $nepaliMonthName, ${nepaliDate.year} BS';
      } catch (e) {
        _adError = 'Invalid date format';
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;
    
    return Scaffold(
      appBar: AppBar(
        title: Text(
          'Nepali Date Converter',
          style: GoogleFonts.poppins(fontWeight: FontWeight.bold),
        ),
        elevation: 0,
      ),
      body: SafeArea(
        child: SingleChildScrollView(
          physics: const BouncingScrollPhysics(),
          child: Padding(
            padding: const EdgeInsets.all(16.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // BS to AD Converter
                Card(
                  elevation: isDarkMode ? 8 : 4,
                  shadowColor: isDarkMode 
                      ? Colors.indigo.withAlpha(100) 
                      : Colors.indigo.withAlpha(60),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(16),
                    side: isDarkMode 
                        ? BorderSide(color: Colors.indigo.withAlpha(80), width: 1)
                        : BorderSide.none,
                  ),
                  child: Padding(
                    padding: const EdgeInsets.all(16.0),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          'BS to AD Converter',
                          style: GoogleFonts.poppins(
                            fontSize: 18,
                            fontWeight: FontWeight.bold,
                            color: Colors.indigo,
                          ),
                        ),
                        const SizedBox(height: 16),
                        
                        // BS Date Input
                        Row(
                          children: [
                            // Year
                            Expanded(
                              child: TextField(
                                controller: _bsYearController,
                                keyboardType: TextInputType.number,
                                decoration: InputDecoration(
                                  labelText: 'Year',
                                  hintText: 'YYYY',
                                  border: OutlineInputBorder(
                                    borderRadius: BorderRadius.circular(12),
                                  ),
                                ),
                              ),
                            ),
                            const SizedBox(width: 8),
                            
                            // Month
                            Expanded(
                              child: TextField(
                                controller: _bsMonthController,
                                keyboardType: TextInputType.number,
                                decoration: InputDecoration(
                                  labelText: 'Month',
                                  hintText: 'MM',
                                  border: OutlineInputBorder(
                                    borderRadius: BorderRadius.circular(12),
                                  ),
                                ),
                              ),
                            ),
                            const SizedBox(width: 8),
                            
                            // Day
                            Expanded(
                              child: TextField(
                                controller: _bsDayController,
                                keyboardType: TextInputType.number,
                                decoration: InputDecoration(
                                  labelText: 'Day',
                                  hintText: 'DD',
                                  border: OutlineInputBorder(
                                    borderRadius: BorderRadius.circular(12),
                                  ),
                                ),
                              ),
                            ),
                          ],
                        ),
                        
                        if (_bsError.isNotEmpty)
                          Padding(
                            padding: const EdgeInsets.only(top: 8.0),
                            child: Text(
                              _bsError,
                              style: GoogleFonts.poppins(
                                color: Colors.red,
                                fontSize: 12,
                              ),
                            ),
                          ),
                        
                        const SizedBox(height: 16),
                        
                        // Convert Button
                        SizedBox(
                          width: double.infinity,
                          child: ElevatedButton(
                            onPressed: _convertBsToAd,
                            style: ElevatedButton.styleFrom(
                              backgroundColor: Colors.indigo,
                              foregroundColor: Colors.white,
                              padding: const EdgeInsets.symmetric(vertical: 12),
                              shape: RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(12),
                              ),
                            ),
                            child: Text(
                              'Convert to AD',
                              style: GoogleFonts.poppins(
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                          ),
                        ),
                        
                        const SizedBox(height: 16),
                        
                        // Result
                        if (_bsToAdResult.isNotEmpty)
                          Container(
                            width: double.infinity,
                            padding: const EdgeInsets.all(12),
                            decoration: BoxDecoration(
                              color: isDarkMode 
                                  ? Colors.indigo.withAlpha(40) 
                                  : Colors.indigo.withAlpha(20),
                              borderRadius: BorderRadius.circular(12),
                              border: Border.all(
                                color: Colors.indigo.withAlpha(100),
                                width: 1,
                              ),
                            ),
                            child: Text(
                              _bsToAdResult,
                              style: GoogleFonts.poppins(
                                fontSize: 16,
                                fontWeight: FontWeight.w500,
                              ),
                              textAlign: TextAlign.center,
                            ),
                          ),
                      ],
                    ),
                  ),
                ),
                
                const SizedBox(height: 24),
                
                // AD to BS Converter
                Card(
                  elevation: isDarkMode ? 8 : 4,
                  shadowColor: isDarkMode 
                      ? Colors.blue.withAlpha(100) 
                      : Colors.blue.withAlpha(60),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(16),
                    side: isDarkMode 
                        ? BorderSide(color: Colors.blue.withAlpha(80), width: 1)
                        : BorderSide.none,
                  ),
                  child: Padding(
                    padding: const EdgeInsets.all(16.0),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          'AD to BS Converter',
                          style: GoogleFonts.poppins(
                            fontSize: 18,
                            fontWeight: FontWeight.bold,
                            color: Colors.blue,
                          ),
                        ),
                        const SizedBox(height: 16),
                        
                        // AD Date Input
                        Row(
                          children: [
                            // Year
                            Expanded(
                              child: TextField(
                                controller: _adYearController,
                                keyboardType: TextInputType.number,
                                decoration: InputDecoration(
                                  labelText: 'Year',
                                  hintText: 'YYYY',
                                  border: OutlineInputBorder(
                                    borderRadius: BorderRadius.circular(12),
                                  ),
                                ),
                              ),
                            ),
                            const SizedBox(width: 8),
                            
                            // Month
                            Expanded(
                              child: TextField(
                                controller: _adMonthController,
                                keyboardType: TextInputType.number,
                                decoration: InputDecoration(
                                  labelText: 'Month',
                                  hintText: 'MM',
                                  border: OutlineInputBorder(
                                    borderRadius: BorderRadius.circular(12),
                                  ),
                                ),
                              ),
                            ),
                            const SizedBox(width: 8),
                            
                            // Day
                            Expanded(
                              child: TextField(
                                controller: _adDayController,
                                keyboardType: TextInputType.number,
                                decoration: InputDecoration(
                                  labelText: 'Day',
                                  hintText: 'DD',
                                  border: OutlineInputBorder(
                                    borderRadius: BorderRadius.circular(12),
                                  ),
                                ),
                              ),
                            ),
                          ],
                        ),
                        
                        if (_adError.isNotEmpty)
                          Padding(
                            padding: const EdgeInsets.only(top: 8.0),
                            child: Text(
                              _adError,
                              style: GoogleFonts.poppins(
                                color: Colors.red,
                                fontSize: 12,
                              ),
                            ),
                          ),
                        
                        const SizedBox(height: 16),
                        
                        // Convert Button
                        SizedBox(
                          width: double.infinity,
                          child: ElevatedButton(
                            onPressed: _convertAdToBs,
                            style: ElevatedButton.styleFrom(
                              backgroundColor: Colors.blue,
                              foregroundColor: Colors.white,
                              padding: const EdgeInsets.symmetric(vertical: 12),
                              shape: RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(12),
                              ),
                            ),
                            child: Text(
                              'Convert to BS',
                              style: GoogleFonts.poppins(
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                          ),
                        ),
                        
                        const SizedBox(height: 16),
                        
                        // Result
                        if (_adToBsResult.isNotEmpty)
                          Container(
                            width: double.infinity,
                            padding: const EdgeInsets.all(12),
                            decoration: BoxDecoration(
                              color: isDarkMode 
                                  ? Colors.blue.withAlpha(40) 
                                  : Colors.blue.withAlpha(20),
                              borderRadius: BorderRadius.circular(12),
                              border: Border.all(
                                color: Colors.blue.withAlpha(100),
                                width: 1,
                              ),
                            ),
                            child: Text(
                              _adToBsResult,
                              style: GoogleFonts.poppins(
                                fontSize: 16,
                                fontWeight: FontWeight.w500,
                              ),
                              textAlign: TextAlign.center,
                            ),
                          ),
                      ],
                    ),
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
