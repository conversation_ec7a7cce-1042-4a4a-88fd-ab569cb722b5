import 'package:flutter/foundation.dart';
import 'dart:io' show Platform;

// This file provides platform checking utilities that work on both web and mobile

// Check if the platform is Android
bool get isAndroid {
  if (kIsWeb) return false;

  try {
    return Platform.isAndroid;
  } catch (e) {
    return false;
  }
}

// Check if the platform is iOS
bool get isIOS {
  if (kIsWeb) return false;

  try {
    return Platform.isIOS;
  } catch (e) {
    return false;
  }
}
