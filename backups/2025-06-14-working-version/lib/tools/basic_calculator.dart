import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';

class BasicCalculator extends StatefulWidget {
  const BasicCalculator({super.key});

  @override
  State<BasicCalculator> createState() => _BasicCalculatorState();
}

class _BasicCalculatorState extends State<BasicCalculator> {
  String _display = '0';
  String _previousNumber = '';
  String _operation = '';
  bool _waitingForOperand = false;

  void _inputNumber(String number) {
    setState(() {
      if (_waitingForOperand) {
        _display = number;
        _waitingForOperand = false;
      } else {
        _display = _display == '0' ? number : _display + number;
      }
    });
  }

  void _inputOperation(String nextOperation) {
    setState(() {
      if (_previousNumber.isEmpty) {
        _previousNumber = _display;
      } else if (!_waitingForOperand) {
        _calculate();
      }

      _waitingForOperand = true;
      _operation = nextOperation;
    });
  }

  void _calculate() {
    double prev = double.tryParse(_previousNumber) ?? 0;
    double current = double.tryParse(_display) ?? 0;
    double result = 0;

    switch (_operation) {
      case '+':
        result = prev + current;
        break;
      case '-':
        result = prev - current;
        break;
      case '×':
        result = prev * current;
        break;
      case '÷':
        if (current != 0) {
          result = prev / current;
        } else {
          _display = 'Error';
          _clear();
          return;
        }
        break;
      default:
        return;
    }

    setState(() {
      _display = result % 1 == 0 ? result.toInt().toString() : result.toString();
      _previousNumber = _display;
      _operation = '';
      _waitingForOperand = true;
    });
  }

  void _clear() {
    setState(() {
      _display = '0';
      _previousNumber = '';
      _operation = '';
      _waitingForOperand = false;
    });
  }

  void _clearEntry() {
    setState(() {
      _display = '0';
    });
  }

  void _inputDecimal() {
    setState(() {
      if (_waitingForOperand) {
        _display = '0.';
        _waitingForOperand = false;
      } else if (!_display.contains('.')) {
        _display = _display + '.';
      }
    });
  }

  void _backspace() {
    setState(() {
      if (_display.length > 1) {
        _display = _display.substring(0, _display.length - 1);
      } else {
        _display = '0';
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(
          'Basic Calculator',
          style: GoogleFonts.poppins(fontWeight: FontWeight.bold),
        ),
        backgroundColor: Colors.blue.shade700,
        foregroundColor: Colors.white,
      ),
      body: Column(
        children: [
          // Display
          Expanded(
            flex: 2,
            child: Container(
              width: double.infinity,
              padding: const EdgeInsets.all(24),
              color: Colors.grey.shade100,
              child: Column(
                mainAxisAlignment: MainAxisAlignment.end,
                crossAxisAlignment: CrossAxisAlignment.end,
                children: [
                  if (_previousNumber.isNotEmpty && _operation.isNotEmpty)
                    Text(
                      '$_previousNumber $_operation',
                      style: GoogleFonts.robotoMono(
                        fontSize: 20,
                        color: Colors.grey.shade600,
                      ),
                    ),
                  const SizedBox(height: 8),
                  Text(
                    _display,
                    style: GoogleFonts.robotoMono(
                      fontSize: 48,
                      fontWeight: FontWeight.bold,
                      color: Colors.black87,
                    ),
                  ),
                ],
              ),
            ),
          ),
          // Buttons
          Expanded(
            flex: 5,
            child: Container(
              padding: const EdgeInsets.all(8),
              child: Column(
                children: [
                  // Row 1: Clear, CE, Backspace, Divide
                  Expanded(
                    child: Row(
                      children: [
                        _buildButton('C', Colors.red.shade400, _clear),
                        _buildButton('CE', Colors.orange.shade400, _clearEntry),
                        _buildButton('⌫', Colors.orange.shade400, _backspace),
                        _buildButton('÷', Colors.blue.shade400, () => _inputOperation('÷')),
                      ],
                    ),
                  ),
                  // Row 2: 7, 8, 9, Multiply
                  Expanded(
                    child: Row(
                      children: [
                        _buildButton('7', Colors.grey.shade300, () => _inputNumber('7')),
                        _buildButton('8', Colors.grey.shade300, () => _inputNumber('8')),
                        _buildButton('9', Colors.grey.shade300, () => _inputNumber('9')),
                        _buildButton('×', Colors.blue.shade400, () => _inputOperation('×')),
                      ],
                    ),
                  ),
                  // Row 3: 4, 5, 6, Subtract
                  Expanded(
                    child: Row(
                      children: [
                        _buildButton('4', Colors.grey.shade300, () => _inputNumber('4')),
                        _buildButton('5', Colors.grey.shade300, () => _inputNumber('5')),
                        _buildButton('6', Colors.grey.shade300, () => _inputNumber('6')),
                        _buildButton('-', Colors.blue.shade400, () => _inputOperation('-')),
                      ],
                    ),
                  ),
                  // Row 4: 1, 2, 3, Add
                  Expanded(
                    child: Row(
                      children: [
                        _buildButton('1', Colors.grey.shade300, () => _inputNumber('1')),
                        _buildButton('2', Colors.grey.shade300, () => _inputNumber('2')),
                        _buildButton('3', Colors.grey.shade300, () => _inputNumber('3')),
                        _buildButton('+', Colors.blue.shade400, () => _inputOperation('+')),
                      ],
                    ),
                  ),
                  // Row 5: 0, Decimal, Equals
                  Expanded(
                    child: Row(
                      children: [
                        _buildButton('0', Colors.grey.shade300, () => _inputNumber('0'), flex: 2),
                        _buildButton('.', Colors.grey.shade300, _inputDecimal),
                        _buildButton('=', Colors.green.shade400, _calculate),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildButton(String text, Color color, VoidCallback onPressed, {int flex = 1}) {
    return Expanded(
      flex: flex,
      child: Container(
        margin: const EdgeInsets.all(4),
        child: ElevatedButton(
          style: ElevatedButton.styleFrom(
            backgroundColor: color,
            foregroundColor: text == 'C' || text == 'CE' || text == '⌫' ? Colors.white : Colors.black87,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(8),
            ),
            elevation: 2,
          ),
          onPressed: onPressed,
          child: Text(
            text,
            style: GoogleFonts.robotoMono(
              fontSize: 24,
              fontWeight: FontWeight.bold,
            ),
          ),
        ),
      ),
    );
  }
}
