class NotificationItem {
  final String id;
  final String title;
  final String message;
  final String category;
  final String url;
  final DateTime timestamp;
  final bool isRead;
  final bool isImportant;

  NotificationItem({
    required this.id,
    required this.title,
    required this.message,
    required this.category,
    required this.url,
    required this.timestamp,
    this.isRead = false,
    this.isImportant = false,
  });

  factory NotificationItem.fromJson(Map<String, dynamic> json) {
    return NotificationItem(
      id: json['id'] ?? '',
      title: json['title'] ?? '',
      message: json['message'] ?? '',
      category: json['category'] ?? '',
      url: json['url'] ?? '',
      timestamp: DateTime.parse(json['timestamp'] ?? DateTime.now().toIso8601String()),
      isRead: json['isRead'] ?? false,
      isImportant: json['isImportant'] ?? false,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'title': title,
      'message': message,
      'category': category,
      'url': url,
      'timestamp': timestamp.toIso8601String(),
      'isRead': isRead,
      'isImportant': isImportant,
    };
  }

  NotificationItem copyWith({
    String? id,
    String? title,
    String? message,
    String? category,
    String? url,
    DateTime? timestamp,
    bool? isRead,
    bool? isImportant,
  }) {
    return NotificationItem(
      id: id ?? this.id,
      title: title ?? this.title,
      message: message ?? this.message,
      category: category ?? this.category,
      url: url ?? this.url,
      timestamp: timestamp ?? this.timestamp,
      isRead: isRead ?? this.isRead,
      isImportant: isImportant ?? this.isImportant,
    );
  }
}
