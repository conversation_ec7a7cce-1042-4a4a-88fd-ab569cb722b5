class Textbook {
  final String title;
  final String subject;
  final String grade;
  final String pdfUrl;
  final String thumbnailUrl;
  final String author;
  final String publisher;
  final int pages;

  Textbook({
    required this.title,
    required this.subject,
    required this.grade,
    required this.pdfUrl,
    required this.thumbnailUrl,
    required this.author,
    required this.publisher,
    required this.pages,
  });

  factory Textbook.fromJson(Map<String, dynamic> json) {
    return Textbook(
      title: json['title'] ?? '',
      subject: json['subject'] ?? '',
      grade: json['grade'] ?? '',
      pdfUrl: json['pdfUrl'] ?? '',
      thumbnailUrl: json['thumbnailUrl'] ?? '',
      author: json['author'] ?? '',
      publisher: json['publisher'] ?? '',
      pages: json['pages'] ?? 0,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'title': title,
      'subject': subject,
      'grade': grade,
      'pdfUrl': pdfUrl,
      'thumbnailUrl': thumbnailUrl,
      'author': author,
      'publisher': publisher,
      'pages': pages,
    };
  }
}

class TextbookMedium {
  final String name;
  final String code;
  final List<Textbook> textbooks;

  TextbookMedium({
    required this.name,
    required this.code,
    required this.textbooks,
  });
}

class TextbookClass {
  final String name;
  final String grade;
  final List<TextbookMedium> mediums;

  TextbookClass({
    required this.name,
    required this.grade,
    required this.mediums,
  });
}
