import 'package:flutter/material.dart';

class NotificationManager {
  static final NotificationManager _instance = NotificationManager._internal();
  factory NotificationManager() => _instance;
  NotificationManager._internal();

  // Simple notification management
  List<Map<String, dynamic>> _notifications = [];

  List<Map<String, dynamic>> get notifications => _notifications;

  void addNotification({
    required String title,
    required String message,
    String category = 'general',
    String url = '',
  }) {
    _notifications.add({
      'id': DateTime.now().millisecondsSinceEpoch.toString(),
      'title': title,
      'message': message,
      'category': category,
      'url': url,
      'timestamp': DateTime.now(),
      'isRead': false,
    });
  }

  void markAsRead(String id) {
    final index = _notifications.indexWhere((n) => n['id'] == id);
    if (index != -1) {
      _notifications[index]['isRead'] = true;
    }
  }

  void clearAll() {
    _notifications.clear();
  }
}
