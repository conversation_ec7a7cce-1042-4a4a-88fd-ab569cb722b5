/// A utility class for converting Romanized Nepali text to Devanagari script.
class RomanizedNepaliConverter {
  /// Map of Romanized Nepali characters to Devanagari script
  static final Map<String, String> _romanToDevanagari = {
    // Vowels
    'a': 'अ',
    'aa': 'आ',
    'i': 'इ',
    'ee': 'ई',
    'u': 'उ',
    'oo': 'ऊ',
    'e': 'ए',
    'ai': 'ऐ',
    'o': 'ओ',
    'au': 'औ',
    'am': 'अं',
    'an': 'अं',
    'ah': 'अः',

    // Consonants
    'ka': 'क',
    'kha': 'ख',
    'ga': 'ग',
    'gha': 'घ',
    'nga': 'ङ',
    'cha': 'च',
    'chha': 'छ',
    'ja': 'ज',
    'jha': 'झ',
    'yna': 'ञ',
    'ta': 'ट',
    'tha': 'ठ',
    'da': 'ड',
    'dha': 'ढ',
    'na': 'न',
    'nna': 'ण',
    'ta.': 'त',
    'tha.': 'थ',
    'da.': 'द',
    'dha.': 'ध',
    'pa': 'प',
    'pha': 'फ',
    'ba': 'ब',
    'bha': 'भ',
    'ma': 'म',
    'ya': 'य',
    'ra': 'र',
    'la': 'ल',
    'wa': 'व',
    'va': 'व',
    'sha': 'श',
    'shha': 'ष',
    'sa': 'स',
    'ha': 'ह',
    'ksha': 'क्ष',
    'tra': 'त्र',
    'gya': 'ज्ञ',

    // Half consonants
    'k': 'क्',
    'kh': 'ख्',
    'g': 'ग्',
    'gh': 'घ्',
    'ng': 'ङ्',
    'ch': 'च्',
    'chh': 'छ्',
    'j': 'ज्',
    'jh': 'झ्',
    'yn': 'ञ्',
    't': 'ट्',
    'th': 'ठ्',
    'd': 'ड्',
    'dh': 'ढ्',
    'n': 'न्',
    'nn': 'ण्',
    't.': 'त्',
    'th.': 'थ्',
    'd.': 'द्',
    'dh.': 'ध्',
    'p': 'प्',
    'ph': 'फ्',
    'b': 'ब्',
    'bh': 'भ्',
    'm': 'म्',
    'y': 'य्',
    'r': 'र्',
    'l': 'ल्',
    'w': 'व्',
    'v': 'व्',
    'sh': 'श्',
    'shh': 'ष्',
    's': 'स्',
    'h': 'ह्',

    // Vowel diacritics
    'aa': 'ा',
    'i': 'ि',
    'ee': 'ी',
    'u': 'ु',
    'oo': 'ू',
    'ri': 'ृ',
    'e': 'े',
    'ai': 'ै',
    'o': 'ो',
    'au': 'ौ',

    // Common words and phrases
    'mero': 'मेरो',
    'timro': 'तिम्रो',
    'hamro': 'हाम्रो',
    'ghar': 'घर',
    'kaha': 'कहाँ',
    'kahan': 'कहाँ',
    'ho': 'हो',
    'ke': 'के',
    'kina': 'किन',
    'kasto': 'कस्तो',
    'ramro': 'राम्रो',
    'namaste': 'नमस्ते',
    'dhanyabad': 'धन्यवाद',
    'sathi': 'साथी',
    'maya': 'माया',
    'prem': 'प्रेम',
    'keti': 'केटी',
    'keta': 'केटा',
    'khai': 'खै',
    'cha': 'छ',
    'chha': 'छ',
    'chhaina': 'छैन',
    'huncha': 'हुन्छ',
    'hunchha': 'हुन्छ',
    'bhayo': 'भयो',
    'thyo': 'थियो',
    'thiyo': 'थियो',
    'xu': 'छु',
    'xau': 'छौ',
    'xan': 'छन्',
    'ma': 'म',
    'timi': 'तिमी',
    'hami': 'हामी',
    'uni': 'उनी',
    'uniharule': 'उनीहरुले',
    'tapai': 'तपाईं',
    'tapaile': 'तपाईंले',
    'malai': 'मलाई',
    'hamilai': 'हामीलाई',
    'timro': 'तिम्रो',
    'mero': 'मेरो',
    'yo': 'यो',
    'tyo': 'त्यो',
    'yaha': 'यहाँ',
    'tyaha': 'त्यहाँ',
    'aba': 'अब',
    'pachi': 'पछि',
    'agadi': 'अगाडि',
    'bhitra': 'भित्र',
    'bahira': 'बाहिर',
    'mathi': 'माथि',
    'tala': 'तल',
    'din': 'दिन',
    'raat': 'रात',
    'bihan': 'बिहान',
    'beluka': 'बेलुका',
    'aaja': 'आज',
    'bholi': 'भोलि',
    'hijo': 'हिजो',
    'parsi': 'पर्सि',
    'ahile': 'अहिले',
    'pahile': 'पहिले',
    'pani': 'पनि',
    'ra': 'र',
    'tara': 'तर',
    'kinaki': 'किनकि',
    'kati': 'कति',
    'dherai': 'धेरै',
    'thorai': 'थोरै',
    'sabai': 'सबै',
    'kehi': 'केही',
    'koi': 'कोही',
    'kasaile': 'कसैले',
    'kasailai': 'कसैलाई',
    'kasaiko': 'कसैको',
    'kasaibata': 'कसैबाट',
    'kasaisanga': 'कसैसँग',
    'kasaima': 'कसैमा',
    'kasaile': 'कसैले',
    'kasailai': 'कसैलाई',
    'kasaiko': 'कसैको',
    'kasaibata': 'कसैबाट',
    'kasaisanga': 'कसैसँग',
    'kasaima': 'कसैमा',

    // Additional common words and phrases
    'naam': 'नाम',
    'desh': 'देश',
    'nepal': 'नेपाल',
    'nepali': 'नेपाली',
    'hun': 'हुन्',
    'hunuhuncha': 'हुनुहुन्छ',
    'sanchai': 'सन्चै',
    'khaana': 'खाना',
    'paani': 'पानी',
    'school': 'स्कुल',
    'college': 'कलेज',
    'university': 'युनिभर्सिटी',
    'kitab': 'किताब',
    'kalam': 'कलम',
    'kapi': 'कपी',
    'gadi': 'गाडी',
    'bus': 'बस',
    'paisa': 'पैसा',
    'rupiya': 'रुपैयाँ',
    'bato': 'बाटो',
    'sadak': 'सडक',
    'gau': 'गाउँ',
    'sahar': 'शहर',
    'gaun': 'गाउँ',
    'shahar': 'शहर',
    'manchhe': 'मान्छे',
    'manche': 'मान्छे',
    'manxe': 'मान्छे',
    'manish': 'मानिस',
    'manis': 'मानिस',
    'saathi': 'साथी',
    'mitho': 'मिठो',
    'mitho cha': 'मिठो छ',
    'ramro cha': 'राम्रो छ',
    'thik cha': 'ठिक छ',
    'thikai cha': 'ठिकै छ',
    'kasto cha': 'कस्तो छ',
    'k cha': 'के छ',
    'k xa': 'के छ',
    'kya ho': 'क्या हो',
    'hajur': 'हजुर',
    'hajur ko': 'हजुरको',
    'hajurko': 'हजुरको',
    'hajur lai': 'हजुरलाई',
    'hajurlai': 'हजुरलाई',
    'hajur le': 'हजुरले',
    'hajurle': 'हजुरले',
    'hajur ma': 'हजुरमा',
    'hajurma': 'हजुरमा',
    'hajur sanga': 'हजुरसँग',
    'hajursanga': 'हजुरसँग',
    'hajur bata': 'हजुरबाट',
    'hajurbata': 'हजुरबाट',
  };

  /// Common Nepali punctuation and symbols
  static final Map<String, String> _punctuation = {
    '?': '?',
    '.': '।',
    ',': ',',
    '!': '!',
    ':': ':',
    ';': ';',
    '"': '"',
    "'": "'",
    '(': '(',
    ')': ')',
    '[': '[',
    ']': ']',
    '{': '{',
    '}': '}',
    '-': '-',
    '_': '_',
    '+': '+',
    '=': '=',
    '/': '/',
    '\\': '\\',
    '|': '|',
    '@': '@',
    '#': '#',
    '\$': '\$',
    '%': '%',
    '^': '^',
    '&': '&',
    '*': '*',
    '<': '<',
    '>': '>',
    '~': '~',
    '`': '`',
  };

  /// Detects if the text is already in Devanagari script
  static bool isDevanagari(String text) {
    // Devanagari Unicode range: U+0900 to U+097F
    final devanagariRegex = RegExp(r'[\u0900-\u097F]');
    return devanagariRegex.hasMatch(text);
  }

  /// Detects if the text is primarily Romanized Nepali
  static bool isRomanized(String text) {
    // Check if the text contains any Devanagari characters
    if (isDevanagari(text)) {
      // Count Devanagari and non-Devanagari characters
      int devanagariCount = 0;
      int nonDevanagariCount = 0;

      for (int i = 0; i < text.length; i++) {
        String char = text[i];
        if (RegExp(r'[\u0900-\u097F]').hasMatch(char)) {
          devanagariCount++;
        } else if (RegExp(r'[a-zA-Z]').hasMatch(char)) {
          nonDevanagariCount++;
        }
      }

      // If more than 50% of the characters are Latin, consider it primarily Romanized
      return nonDevanagariCount > devanagariCount;
    }

    // If no Devanagari characters are found, check if it contains Latin characters
    return RegExp(r'[a-zA-Z]').hasMatch(text);
  }

  /// Determines the dominant script in the text (Devanagari or Romanized)
  static String detectScriptType(String text) {
    if (text.isEmpty) return 'unknown';

    // If the text contains no Devanagari characters, it's Romanized
    if (!isDevanagari(text)) return 'romanized';

    // If the text contains no Latin characters, it's Devanagari
    if (!RegExp(r'[a-zA-Z]').hasMatch(text)) return 'devanagari';

    // Count Devanagari and Latin characters
    int devanagariCount = 0;
    int latinCount = 0;

    for (int i = 0; i < text.length; i++) {
      String char = text[i];
      if (RegExp(r'[\u0900-\u097F]').hasMatch(char)) {
        devanagariCount++;
      } else if (RegExp(r'[a-zA-Z]').hasMatch(char)) {
        latinCount++;
      }
    }

    // Determine the dominant script
    if (devanagariCount >= latinCount) {
      return 'devanagari';
    } else {
      return 'romanized';
    }
  }

  /// Converts Romanized Nepali text to Devanagari script
  static String convertToDevanagari(String romanizedText) {
    // If the text is already in Devanagari, return it as is
    if (isDevanagari(romanizedText)) {
      return romanizedText;
    }

    // Split the text into words
    List<String> words = romanizedText.split(' ');
    List<String> convertedWords = [];

    for (String word in words) {
      // Check if the word is a common word
      if (_romanToDevanagari.containsKey(word.toLowerCase())) {
        convertedWords.add(_romanToDevanagari[word.toLowerCase()]!);
      } else {
        // Process character by character for complex words
        String convertedWord = _processWord(word);
        convertedWords.add(convertedWord);
      }
    }

    return convertedWords.join(' ');
  }

  /// Process a word character by character
  static String _processWord(String word) {
    String result = '';
    int i = 0;

    while (i < word.length) {
      // Check for punctuation
      if (_punctuation.containsKey(word[i])) {
        result += _punctuation[word[i]]!;
        i++;
        continue;
      }

      // Check for 3-character combinations
      if (i + 2 < word.length) {
        String threeChars = word.substring(i, i + 3).toLowerCase();
        if (_romanToDevanagari.containsKey(threeChars)) {
          result += _romanToDevanagari[threeChars]!;
          i += 3;
          continue;
        }
      }

      // Check for 2-character combinations
      if (i + 1 < word.length) {
        String twoChars = word.substring(i, i + 2).toLowerCase();
        if (_romanToDevanagari.containsKey(twoChars)) {
          result += _romanToDevanagari[twoChars]!;
          i += 2;
          continue;
        }
      }

      // Single character
      String oneChar = word[i].toLowerCase();
      if (_romanToDevanagari.containsKey(oneChar)) {
        result += _romanToDevanagari[oneChar]!;
      } else {
        // If not found, keep the original character
        result += word[i];
      }
      i++;
    }

    return result;
  }
}
