name: nepali_results_working
description: "Nepali Results App with IPO Section"
publish_to: 'none'

version: 1.0.0+1

environment:
  sdk: ^3.7.2

dependencies:
  flutter:
    sdk: flutter
  cupertino_icons: ^1.0.8
  url_launcher: ^6.2.5
  google_fonts: ^6.1.0
  html: ^0.15.4
  provider: ^6.1.1
  shared_preferences: ^2.2.2
  http: ^1.1.0
  webview_flutter: ^4.4.2
  nepali_utils: ^3.0.5
  intl: ^0.19.0
  # Temporarily commented out Firebase dependencies to fix web build issues
  # firebase_messaging: ^14.7.10
  # firebase_core: ^2.24.2
  flutter_custom_tabs: ^2.1.0
  share_plus: ^7.2.2
  flutter_rating_bar: ^4.0.1

dev_dependencies:
  flutter_test:
    sdk: flutter
  flutter_lints: ^5.0.0
  flutter_launcher_icons: ^0.13.1

flutter:
  uses-material-design: true
  assets:
    - assets/
    - assets/images/
    - assets/flags/

flutter_launcher_icons:
  android: "launcher_icon"
  ios: false
  image_path: "assets/images/app_icon5.png"
  min_sdk_android: 21