import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:google_fonts/google_fonts.dart';
import '../utils/notification_provider.dart';

class NotificationSettingsScreen extends StatelessWidget {
  const NotificationSettingsScreen({super.key});

  @override
  Widget build(BuildContext context) {
    // Clear notification count when this screen is opened
    WidgetsBinding.instance.addPostFrameCallback((_) {
      Provider.of<NotificationProvider>(
        context,
        listen: false,
      ).clearNotificationCount();
    });

    return Scaffold(
      appBar: AppBar(
        title: Text(
          'Notification Settings',
          style: GoogleFonts.poppins(fontWeight: FontWeight.bold),
        ),
        elevation: 0,
      ),
      body: Consumer<NotificationProvider>(
        builder: (context, notificationProvider, child) {
          return ListView(
            padding: const EdgeInsets.all(16.0),
            children: [
              // Main notification toggle
              _buildMainNotificationToggle(context, notificationProvider),

              const Divider(height: 32),

              // Notification categories
              _buildNotificationCategorySection(context, notificationProvider),

              const SizedBox(height: 24),

              // Test notification button
              if (notificationProvider.areNotificationsEnabled)
                _buildTestNotificationButton(context, notificationProvider),
            ],
          );
        },
      ),
    );
  }

  Widget _buildMainNotificationToggle(
    BuildContext context,
    NotificationProvider notificationProvider,
  ) {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Notifications',
              style: GoogleFonts.poppins(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: Theme.of(context).colorScheme.primary,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              'Receive important updates about results, news, and reminders',
              style: GoogleFonts.poppins(
                fontSize: 14,
                color: Theme.of(context).textTheme.bodyMedium?.color,
              ),
            ),
            const SizedBox(height: 16),
            SwitchListTile(
              title: Text(
                'Enable Notifications',
                style: GoogleFonts.poppins(fontWeight: FontWeight.w500),
              ),
              value: notificationProvider.areNotificationsEnabled,
              onChanged: (value) {
                notificationProvider.toggleNotifications(value);
              },
              secondary: Icon(
                Icons.notifications,
                color: Theme.of(context).colorScheme.primary,
              ),
              contentPadding: EdgeInsets.zero,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildNotificationCategorySection(
    BuildContext context,
    NotificationProvider notificationProvider,
  ) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Notification Categories',
          style: GoogleFonts.poppins(
            fontSize: 18,
            fontWeight: FontWeight.bold,
            color: Theme.of(context).colorScheme.primary,
          ),
        ),
        const SizedBox(height: 16),

        // Categories
        _buildCategoryCard(
          context,
          title: 'Result Notifications',
          description: 'Get notified when new results are published',
          icon: Icons.school,
          color: Colors.blue,
          isEnabled: notificationProvider.areResultNotificationsEnabled,
          isMainEnabled: notificationProvider.areNotificationsEnabled,
          onChanged: (value) {
            notificationProvider.toggleResultNotifications(value);
          },
        ),

        const SizedBox(height: 12),

        _buildCategoryCard(
          context,
          title: 'News Notifications',
          description: 'Stay updated with the latest education news',
          icon: Icons.newspaper,
          color: Colors.green,
          isEnabled: notificationProvider.areNewsNotificationsEnabled,
          isMainEnabled: notificationProvider.areNotificationsEnabled,
          onChanged: (value) {
            notificationProvider.toggleNewsNotifications(value);
          },
        ),

        const SizedBox(height: 12),

        _buildCategoryCard(
          context,
          title: 'Reminder Notifications',
          description: 'Get reminders about upcoming exams and deadlines',
          icon: Icons.alarm,
          color: Colors.orange,
          isEnabled: notificationProvider.areReminderNotificationsEnabled,
          isMainEnabled: notificationProvider.areNotificationsEnabled,
          onChanged: (value) {
            notificationProvider.toggleReminderNotifications(value);
          },
        ),
      ],
    );
  }

  Widget _buildCategoryCard(
    BuildContext context, {
    required String title,
    required String description,
    required IconData icon,
    required Color color,
    required bool isEnabled,
    required bool isMainEnabled,
    required ValueChanged<bool> onChanged,
  }) {
    return Card(
      elevation: 1,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: color.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Icon(icon, color: color, size: 24),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Text(
                    title,
                    style: GoogleFonts.poppins(
                      fontSize: 16,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ),
                Switch(
                  value: isEnabled,
                  onChanged: isMainEnabled ? onChanged : null,
                ),
              ],
            ),
            const SizedBox(height: 8),
            Padding(
              padding: const EdgeInsets.only(left: 44.0),
              child: Text(
                description,
                style: GoogleFonts.poppins(
                  fontSize: 14,
                  color: Theme.of(context).textTheme.bodyMedium?.color,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildTestNotificationButton(
    BuildContext context,
    NotificationProvider notificationProvider,
  ) {
    // Controller for scheduled notification time
    final scheduledTimeController = TextEditingController(
      text: DateTime.now().add(const Duration(minutes: 1)).toString(),
    );

    return Column(
      children: [
        // Immediate notification
        Card(
          margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
          child: Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Send Immediate Notification',
                  style: GoogleFonts.poppins(
                    fontWeight: FontWeight.bold,
                    fontSize: 16,
                  ),
                ),
                const SizedBox(height: 8),
                Text(
                  'This will send a notification immediately that will appear in your notification tray.',
                  style: GoogleFonts.poppins(fontSize: 14),
                ),
                const SizedBox(height: 16),
                Center(
                  child: ElevatedButton.icon(
                    onPressed: () {
                      notificationProvider.showNotification(
                        id: 0,
                        title: 'Test Notification',
                        body:
                            'This is a test notification from Nepali Results app',
                        data: {'type': 'test'},
                      );

                      ScaffoldMessenger.of(context).showSnackBar(
                        const SnackBar(
                          content: Text('Test notification sent'),
                          duration: Duration(seconds: 2),
                        ),
                      );
                    },
                    icon: const Icon(Icons.send),
                    label: Text('Send Now', style: GoogleFonts.poppins()),
                    style: ElevatedButton.styleFrom(
                      padding: const EdgeInsets.symmetric(
                        horizontal: 16,
                        vertical: 12,
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),
        ),

        // Scheduled notification
        Card(
          margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
          child: Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Schedule a Notification',
                  style: GoogleFonts.poppins(
                    fontWeight: FontWeight.bold,
                    fontSize: 16,
                  ),
                ),
                const SizedBox(height: 8),
                Text(
                  'Schedule a notification to be delivered at a specific time.',
                  style: GoogleFonts.poppins(fontSize: 14),
                ),
                const SizedBox(height: 16),
                TextField(
                  controller: scheduledTimeController,
                  decoration: InputDecoration(
                    labelText: 'Scheduled Time',
                    hintText: 'YYYY-MM-DD HH:MM:SS',
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(8),
                    ),
                  ),
                ),
                const SizedBox(height: 16),
                Center(
                  child: ElevatedButton.icon(
                    onPressed: () {
                      try {
                        final scheduledTime = DateTime.parse(
                          scheduledTimeController.text,
                        );

                        if (scheduledTime.isBefore(DateTime.now())) {
                          ScaffoldMessenger.of(context).showSnackBar(
                            const SnackBar(
                              content: Text('Please select a future time'),
                              duration: Duration(seconds: 2),
                            ),
                          );
                          return;
                        }

                        notificationProvider.scheduleNotification(
                          id: 1,
                          title: 'Scheduled Notification',
                          body:
                              'This notification was scheduled for ${scheduledTime.toString()}',
                          scheduledDate: scheduledTime,
                          data: {'type': 'scheduled'},
                        );

                        ScaffoldMessenger.of(context).showSnackBar(
                          SnackBar(
                            content: Text(
                              'Notification scheduled for ${scheduledTime.toString()}',
                            ),
                            duration: const Duration(seconds: 2),
                          ),
                        );
                      } catch (e) {
                        ScaffoldMessenger.of(context).showSnackBar(
                          SnackBar(
                            content: Text('Invalid date format: $e'),
                            duration: const Duration(seconds: 2),
                          ),
                        );
                      }
                    },
                    icon: const Icon(Icons.schedule),
                    label: Text('Schedule', style: GoogleFonts.poppins()),
                    style: ElevatedButton.styleFrom(
                      padding: const EdgeInsets.symmetric(
                        horizontal: 16,
                        vertical: 12,
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),
        ),

        // Badge simulation
        Card(
          margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
          child: Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Notification Badge',
                  style: GoogleFonts.poppins(
                    fontWeight: FontWeight.bold,
                    fontSize: 16,
                  ),
                ),
                const SizedBox(height: 8),
                Text(
                  'Simulate a new notification to update the badge count in the app bar.',
                  style: GoogleFonts.poppins(fontSize: 14),
                ),
                const SizedBox(height: 16),
                Center(
                  child: OutlinedButton.icon(
                    onPressed: () {
                      notificationProvider.simulateNewNotification();

                      ScaffoldMessenger.of(context).showSnackBar(
                        const SnackBar(
                          content: Text(
                            'New notification simulated (badge updated)',
                          ),
                          duration: Duration(seconds: 2),
                        ),
                      );
                    },
                    icon: const Icon(Icons.notifications_active),
                    label: Text(
                      'Update Badge Count',
                      style: GoogleFonts.poppins(),
                    ),
                    style: OutlinedButton.styleFrom(
                      padding: const EdgeInsets.symmetric(
                        horizontal: 16,
                        vertical: 12,
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }
}
