import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';

class GpaCalculatorScreen extends StatefulWidget {
  final String faculty;

  const GpaCalculatorScreen({super.key, required this.faculty});

  @override
  State<GpaCalculatorScreen> createState() => _GpaCalculatorScreenState();
}

class _GpaCalculatorScreenState extends State<GpaCalculatorScreen> {
  bool _isClass11Selected = true;
  late List<String> _facultySubjects;

  // Grade values
  final Map<String, double> _gradeValues = {
    'A+': 4.0,
    'A': 3.6,
    'B+': 3.2,
    'B': 2.8,
    'C+': 2.4,
    'C': 2.0,
    'D+': 1.6,
    'D': 1.2,
    'E': 0.8,
    'N/A': 0.0,
  };

  // Science subjects
  final List<String> _scienceSubjects = [
    'Compulsory English',
    'Compulsory Nepali',
    'Physics',
    'Chemistry',
    'Biology',
    'Mathematics',
    'Computer Science',
    'Statistics',
  ];

  // Arts subjects
  final List<String> _artsSubjects = [
    'Compulsory English',
    'Compulsory Nepali',
    'Sociology',
    'Geography',
    'History',
    'Political Science',
    'Psychology',
    'Home Science',
    'Fine Arts',
    'Mass Communication',
    'Rural Development',
  ];

  // Commerce subjects
  final List<String> _commerceSubjects = [
    'Compulsory English',
    'Compulsory Nepali',
    'Accountancy',
    'Business Studies',
    'Economics',
    'Business Mathematics',
    'Marketing',
    'Hotel Management',
    'Travel & Tourism',
    'Computer Science',
  ];

  // Selected subjects and grades
  final List<String> _selectedSubjects = [];
  final Map<String, String> _selectedGrades = {};

  // Results
  double _calculatedGpa = 0.0;
  String _remark = '';
  bool _hasCalculated = false;

  @override
  void initState() {
    super.initState();
    // Initialize faculty subjects based on selected faculty
    _initializeFacultySubjects();
    _resetForm();
  }

  void _initializeFacultySubjects() {
    switch (widget.faculty) {
      case 'Science':
        _facultySubjects = _scienceSubjects;
        break;
      case 'Arts':
        _facultySubjects = _artsSubjects;
        break;
      case 'Commerce':
        _facultySubjects = _commerceSubjects;
        break;
      default:
        _facultySubjects = _scienceSubjects;
    }
  }

  void _resetForm() {
    setState(() {
      _selectedSubjects.clear();
      _selectedGrades.clear();
      _hasCalculated = false;

      // Add default subjects based on faculty
      final subjectCount =
          _facultySubjects.length >= 5 ? 5 : _facultySubjects.length;
      _selectedSubjects.addAll(
        _facultySubjects.sublist(0, subjectCount),
      ); // Add first 5 subjects by default

      // Set default grades to N/A
      for (var subject in _selectedSubjects) {
        _selectedGrades[subject] = 'N/A';
      }
    });
  }

  void _calculateGpa() {
    double totalPoints = 0;
    int validSubjectsCount = 0;

    for (var subject in _selectedSubjects) {
      final grade = _selectedGrades[subject] ?? 'N/A';
      if (grade != 'N/A') {
        totalPoints += _gradeValues[grade] ?? 0;
        validSubjectsCount++;
      }
    }

    setState(() {
      if (validSubjectsCount > 0) {
        _calculatedGpa = totalPoints / validSubjectsCount;
        _hasCalculated = true;
        _remark = _getRemarkForGpa(_calculatedGpa);
      } else {
        _calculatedGpa = 0.0;
        _hasCalculated = true;
        _remark = 'Please select at least one grade';
      }
    });
  }

  String _getRemarkForGpa(double gpa) {
    if (gpa >= 3.6) return 'Outstanding';
    if (gpa >= 3.2) return 'Excellent';
    if (gpa >= 2.8) return 'Very Good';
    if (gpa >= 2.4) return 'Good';
    if (gpa >= 2.0) return 'Satisfactory';
    if (gpa >= 1.6) return 'Acceptable';
    if (gpa >= 1.2) return 'Basic';
    return 'Needs Improvement';
  }

  Color _getFacultyColor() {
    switch (widget.faculty) {
      case 'Science':
        return Colors.blue;
      case 'Arts':
        return Colors.purple;
      case 'Commerce':
        return Colors.amber.shade800;
      default:
        return Theme.of(context).colorScheme.primary;
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: const Text('NEB GPA Calculator')),
      body: SafeArea(
        child: SingleChildScrollView(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Title
              Center(
                child: Text(
                  'NEB GPA Calculator',
                  style: GoogleFonts.poppins(
                    fontSize: 20,
                    fontWeight: FontWeight.bold,
                    color: Theme.of(context).colorScheme.primary,
                  ),
                ),
              ),
              const SizedBox(height: 8),

              // Faculty name
              Center(
                child: Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 16,
                    vertical: 8,
                  ),
                  decoration: BoxDecoration(
                    color: _getFacultyColor().withOpacity(0.1),
                    borderRadius: BorderRadius.circular(20),
                  ),
                  child: Text(
                    '${widget.faculty} Faculty',
                    style: GoogleFonts.poppins(
                      fontSize: 16,
                      fontWeight: FontWeight.w600,
                      color: _getFacultyColor(),
                    ),
                  ),
                ),
              ),
              const SizedBox(height: 16),

              // Class Selection
              Row(
                children: [
                  Expanded(
                    child: ElevatedButton(
                      onPressed: () {
                        setState(() {
                          _isClass11Selected = true;
                          _resetForm();
                        });
                      },
                      style: ElevatedButton.styleFrom(
                        backgroundColor:
                            _isClass11Selected
                                ? Theme.of(context).colorScheme.primary
                                : Theme.of(
                                  context,
                                ).colorScheme.surfaceContainerHighest,
                        foregroundColor:
                            _isClass11Selected
                                ? Theme.of(context).colorScheme.onPrimary
                                : Theme.of(
                                  context,
                                ).colorScheme.onSurfaceVariant,
                        padding: const EdgeInsets.symmetric(vertical: 12),
                        shape: const RoundedRectangleBorder(
                          borderRadius: BorderRadius.only(
                            topLeft: Radius.circular(8),
                            bottomLeft: Radius.circular(8),
                          ),
                        ),
                      ),
                      child: Text(
                        'Class 11',
                        style: GoogleFonts.poppins(fontWeight: FontWeight.w600),
                      ),
                    ),
                  ),
                  Expanded(
                    child: ElevatedButton(
                      onPressed: () {
                        setState(() {
                          _isClass11Selected = false;
                          _resetForm();
                        });
                      },
                      style: ElevatedButton.styleFrom(
                        backgroundColor:
                            !_isClass11Selected
                                ? Theme.of(context).colorScheme.primary
                                : Theme.of(
                                  context,
                                ).colorScheme.surfaceContainerHighest,
                        foregroundColor:
                            !_isClass11Selected
                                ? Theme.of(context).colorScheme.onPrimary
                                : Theme.of(
                                  context,
                                ).colorScheme.onSurfaceVariant,
                        padding: const EdgeInsets.symmetric(vertical: 12),
                        shape: const RoundedRectangleBorder(
                          borderRadius: BorderRadius.only(
                            topRight: Radius.circular(8),
                            bottomRight: Radius.circular(8),
                          ),
                        ),
                      ),
                      child: Text(
                        'Class 12',
                        style: GoogleFonts.poppins(fontWeight: FontWeight.w600),
                      ),
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 24),

              // Subject Selection
              Text(
                'Select Subjects and Grades',
                style: GoogleFonts.poppins(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                  color: Theme.of(context).colorScheme.primary,
                ),
              ),
              const SizedBox(height: 8),

              // Subject List
              ..._selectedSubjects.map(
                (subject) => _buildSubjectGradeRow(subject),
              ),

              // Add Subject Button
              if (_selectedSubjects.length < _facultySubjects.length)
                Padding(
                  padding: const EdgeInsets.symmetric(vertical: 8.0),
                  child: OutlinedButton.icon(
                    onPressed: _showAddSubjectDialog,
                    icon: const Icon(Icons.add),
                    label: const Text('Add Subject'),
                    style: OutlinedButton.styleFrom(
                      foregroundColor: Theme.of(context).colorScheme.primary,
                    ),
                  ),
                ),

              const SizedBox(height: 24),

              // Calculate Button
              SizedBox(
                width: double.infinity,
                child: ElevatedButton(
                  onPressed: _calculateGpa,
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Theme.of(context).colorScheme.primary,
                    foregroundColor: Theme.of(context).colorScheme.onPrimary,
                    padding: const EdgeInsets.symmetric(vertical: 16),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(8),
                    ),
                  ),
                  child: Text(
                    'Calculate GPA',
                    style: GoogleFonts.poppins(
                      fontWeight: FontWeight.bold,
                      fontSize: 16,
                    ),
                  ),
                ),
              ),

              const SizedBox(height: 16),

              // Clear Button
              SizedBox(
                width: double.infinity,
                child: OutlinedButton(
                  onPressed: _resetForm,
                  style: OutlinedButton.styleFrom(
                    foregroundColor: Theme.of(context).colorScheme.primary,
                    padding: const EdgeInsets.symmetric(vertical: 16),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(8),
                    ),
                  ),
                  child: Text(
                    'Clear Form',
                    style: GoogleFonts.poppins(
                      fontWeight: FontWeight.w500,
                      fontSize: 16,
                    ),
                  ),
                ),
              ),

              const SizedBox(height: 24),

              // Results
              if (_hasCalculated)
                Card(
                  elevation: 4,
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Padding(
                    padding: const EdgeInsets.all(16.0),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          'Your GPA Result',
                          style: GoogleFonts.poppins(
                            fontSize: 18,
                            fontWeight: FontWeight.bold,
                            color: Theme.of(context).colorScheme.primary,
                          ),
                        ),
                        const SizedBox(height: 16),
                        Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            Text(
                              'GPA:',
                              style: GoogleFonts.poppins(
                                fontSize: 16,
                                fontWeight: FontWeight.w500,
                              ),
                            ),
                            Text(
                              _calculatedGpa.toStringAsFixed(2),
                              style: GoogleFonts.poppins(
                                fontSize: 24,
                                fontWeight: FontWeight.bold,
                                color: Theme.of(context).colorScheme.primary,
                              ),
                            ),
                          ],
                        ),
                        const SizedBox(height: 8),
                        Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            Text(
                              'Remark:',
                              style: GoogleFonts.poppins(
                                fontSize: 16,
                                fontWeight: FontWeight.w500,
                              ),
                            ),
                            Text(
                              _remark,
                              style: GoogleFonts.poppins(
                                fontSize: 18,
                                fontWeight: FontWeight.bold,
                                color: _getColorForRemark(_remark),
                              ),
                            ),
                          ],
                        ),
                      ],
                    ),
                  ),
                ),
            ],
          ),
        ),
      ),
    );
  }

  Color _getColorForRemark(String remark) {
    switch (remark) {
      case 'Outstanding':
        return Colors.purple;
      case 'Excellent':
        return Colors.indigo;
      case 'Very Good':
        return Colors.blue;
      case 'Good':
        return Colors.green;
      case 'Satisfactory':
        return Colors.amber.shade700;
      case 'Acceptable':
        return Colors.orange;
      case 'Basic':
        return Colors.deepOrange;
      default:
        return Colors.red;
    }
  }

  Widget _buildSubjectGradeRow(String subject) {
    return Card(
      margin: const EdgeInsets.symmetric(vertical: 4),
      child: Padding(
        padding: const EdgeInsets.all(12.0),
        child: Row(
          children: [
            Expanded(
              flex: 3,
              child: Text(
                subject,
                style: GoogleFonts.poppins(fontWeight: FontWeight.w500),
              ),
            ),
            Expanded(
              flex: 2,
              child: DropdownButtonFormField<String>(
                value: _selectedGrades[subject],
                decoration: const InputDecoration(
                  contentPadding: EdgeInsets.symmetric(
                    horizontal: 12,
                    vertical: 8,
                  ),
                  border: OutlineInputBorder(),
                ),
                items:
                    _gradeValues.keys.map((grade) {
                      return DropdownMenuItem<String>(
                        value: grade,
                        child: Text(grade),
                      );
                    }).toList(),
                onChanged: (value) {
                  setState(() {
                    _selectedGrades[subject] = value!;
                  });
                },
              ),
            ),
            IconButton(
              icon: const Icon(Icons.delete_outline, color: Colors.red),
              onPressed: () {
                setState(() {
                  _selectedSubjects.remove(subject);
                  _selectedGrades.remove(subject);
                });
              },
            ),
          ],
        ),
      ),
    );
  }

  void _showAddSubjectDialog() {
    final availableSubjects =
        _facultySubjects.where((s) => !_selectedSubjects.contains(s)).toList();

    if (availableSubjects.isEmpty) return;

    String? selectedSubject = availableSubjects.first;

    showDialog(
      context: context,
      builder: (context) {
        return AlertDialog(
          title: const Text('Add Subject'),
          content: StatefulBuilder(
            builder: (context, setState) {
              return DropdownButtonFormField<String>(
                value: selectedSubject,
                decoration: const InputDecoration(
                  labelText: 'Select Subject',
                  border: OutlineInputBorder(),
                ),
                items:
                    availableSubjects.map((subject) {
                      return DropdownMenuItem<String>(
                        value: subject,
                        child: Text(subject),
                      );
                    }).toList(),
                onChanged: (value) {
                  setState(() {
                    selectedSubject = value;
                  });
                },
              );
            },
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.pop(context),
              child: const Text('Cancel'),
            ),
            ElevatedButton(
              onPressed: () {
                if (selectedSubject != null) {
                  setState(() {
                    _selectedSubjects.add(selectedSubject!);
                    _selectedGrades[selectedSubject!] = 'N/A';
                  });
                }
                Navigator.pop(context);
              },
              child: const Text('Add'),
            ),
          ],
        );
      },
    );
  }
}
