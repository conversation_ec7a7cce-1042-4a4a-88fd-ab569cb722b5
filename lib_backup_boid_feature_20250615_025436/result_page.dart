import 'package:flutter/material.dart';
import 'package:url_launcher/url_launcher.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:provider/provider.dart';
import 'result_category.dart';

import 'services/chrome_custom_tabs_service.dart';
import 'utils/notification_provider.dart';

class ResultPage extends StatefulWidget {
  final ResultCategory category;

  const ResultPage({super.key, required this.category});

  @override
  State<ResultPage> createState() => _ResultPageState();
}

class _ResultPageState extends State<ResultPage> {
  // State variables for university category selection
  String? selectedTUCategory;
  String? selectedTUYear;
  String? selectedKUCategory;
  String? selectedKUYear;
  String? selectedPUCategory;
  String? selectedPUYear;

  void _openWebView(BuildContext context, String title, String url) async {
    // Use Chrome Custom Tabs instead of WebView
    try {
      await ChromeCustomTabsService.launch(url, title: title);
    } catch (e) {
      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Could not open $title: ${e.toString()}')),
        );
      }
    }
  }

  void _searchOnGoogle(BuildContext context) {
    final searchQuery = '${widget.category.name} results nepal';
    final googleUrl =
        'https://www.google.com/search?q=${Uri.encodeComponent(searchQuery)}';

    _openWebView(
      context,
      widget.category.name == 'HSEB'
          ? 'Google Search - Higher Secondary Education Board Result'
          : 'Google Search - ${widget.category.name}',
      googleUrl,
    );
  }

  @override
  Widget build(BuildContext context) {
    return SafeArea(
      child: Scaffold(
        appBar: AppBar(
          toolbarHeight: kToolbarHeight + 24, // Add 3 units (24px) of height
          backgroundColor: Colors.transparent,
          elevation: 0,
          automaticallyImplyLeading: false,
          flexibleSpace: Container(
            padding: const EdgeInsets.only(top: 24), // Lower AppBar by 3 units
            alignment: Alignment.centerLeft,
            child: Row(
              children: [
                Padding(
                  padding: const EdgeInsets.only(left: 8.0, top: 8.0), // Lower back arrow by 1 step
                  child: IconButton(
                    icon: const Icon(Icons.arrow_back),
                    onPressed: () => Navigator.of(context).pop(),
                  ),
                ),
                Expanded(
                  child: Padding(
                    padding: const EdgeInsets.only(left: 8.0), // Adjust text position
                    child: Text(
                      widget.category.name == 'HSEB'
                          ? 'HSEB Result'
                          : widget.category.name == 'TU'
                          ? 'TU Results'
                          : widget.category.name == 'DV'
                          ? 'DV Lottery Result'
                          : widget.category.name == 'IPO'
                          ? 'IPO Result'
                          : '${widget.category.name} Results',
                      style: Theme.of(context).textTheme.titleLarge?.copyWith(
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),
        ),
      body: SafeArea(
        child: SingleChildScrollView(
          child: Padding(
            padding: const EdgeInsets.all(16.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Header Card - Only for categories other than SEE, HSEB, TU, KU, PU, CTEVT, DV, and IPO
                if (widget.category.name != 'SEE' &&
                    widget.category.name != 'HSEB' &&
                    widget.category.name != 'TU' &&
                    widget.category.name != 'KU' &&
                    widget.category.name != 'PU' &&
                    widget.category.name != 'CTEVT' &&
                    widget.category.name != 'DV' &&
                    widget.category.name != 'IPO')
                  Card(
                    child: Padding(
                      padding: const EdgeInsets.all(16.0),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Row(
                            children: [
                              CircleAvatar(
                                backgroundColor: Theme.of(
                                  context,
                                ).colorScheme.primary.withAlpha(25),
                                radius: 24,
                                child: Icon(
                                  widget.category.icon,
                                  color: Theme.of(context).colorScheme.primary,
                                  size: 28,
                                ),
                              ),
                              const SizedBox(width: 16),
                              Expanded(
                                child: Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    Text(
                                      widget.category.name,
                                      style: Theme.of(
                                        context,
                                      ).textTheme.titleLarge?.copyWith(
                                        fontWeight: FontWeight.bold,
                                      ),
                                    ),
                                    const SizedBox(height: 4),
                                    Text(
                                      widget.category.description,
                                      style:
                                          Theme.of(
                                            context,
                                          ).textTheme.bodyMedium,
                                    ),
                                  ],
                                ),
                              ),
                            ],
                          ),
                        ],
                      ),
                    ),
                  ),

                // Removed SEE/HSEB Headers
                const SizedBox(height: 16),

                // Official Websites Section
                Text(
                  'Official Websites',
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 8),

                // Website List
                if (widget.category.name == 'SEE' ||
                    widget.category.name == 'HSEB' ||
                    widget.category.name == 'TU' ||
                    widget.category.name == 'KU' ||
                    widget.category.name == 'PU' ||
                    widget.category.name == 'CTEVT' ||
                    widget.category.name == 'DV' ||
                    widget.category.name == 'IPO')
                  // Vertical layout for SEE, HSEB, and TU with image assets
                  Column(
                    children:
                        widget.category.resultUrls.entries.map((entry) {
                          // Custom styling based on the website
                          Color buttonColor =
                              Theme.of(context).colorScheme.primary; // Default
                          String imagePath =
                              'assets/images/official site.png'; // Default
                          String label = entry.key; // Default
                          String subtitle = 'View result online'; // Default

                          // SEE specific styling
                          if (widget.category.name == 'SEE') {
                            if (entry.key == 'Official Site') {
                              buttonColor = const Color(
                                0xFF0C4DA2,
                              ); // Government blue
                              imagePath =
                                  'assets/images/official site copy.png';
                              label = 'Official Site';
                              subtitle = 'Government official results';
                            } else if (entry.key == 'Check via NTC') {
                              buttonColor = const Color(0xFF00A9CE); // NTC teal
                              imagePath = 'assets/images/nepal telecom.png';
                              label = 'NTC Portal';
                              subtitle = 'Nepal Telecom portal';
                            } else if (entry.key == 'View on eKantipur') {
                              buttonColor = const Color(
                                0xFF9E1B32,
                              ); // eKantipur maroon
                              imagePath = 'assets/images/ekantipur.png';
                              label = 'eKantipur';
                              subtitle = 'With detailed marksheet';
                            } else {
                              buttonColor =
                                  Theme.of(context).colorScheme.primary;
                              imagePath =
                                  'assets/images/official site.png'; // Default
                              label = entry.key;
                              subtitle = 'View result online';
                            }
                          }
                          // HSEB specific styling
                          else if (widget.category.name == 'HSEB') {
                            if (entry.key == 'Official Website') {
                              buttonColor = const Color(
                                0xFF0C4DA2,
                              ); // Government blue
                              imagePath = 'assets/images/hseb_results.png';
                              label = 'Official Site';
                              subtitle = 'Government official results';
                            } else if (entry.key == 'NTC Portal') {
                              buttonColor = const Color(0xFF00A9CE); // NTC teal
                              imagePath = 'assets/images/nepal telecom.png';
                              label = 'NTC Portal';
                              subtitle = 'Nepal Telecom portal';
                            } else {
                              buttonColor =
                                  Theme.of(context).colorScheme.primary;
                              imagePath =
                                  'assets/images/official site.png'; // Default
                              label = entry.key;
                              subtitle = 'View result online';
                            }
                          }
                          // TU specific styling
                          else if (widget.category.name == 'TU') {
                            if (entry.key == 'Official Website') {
                              buttonColor = const Color(
                                0xFF673AB7,
                              ); // Deep Purple
                              imagePath = 'assets/images/tu_results.png';
                              label = 'Official Site';
                              subtitle =
                                  'Tribhuvan University official results';
                            } else {
                              buttonColor =
                                  Theme.of(context).colorScheme.primary;
                              imagePath =
                                  'assets/images/official site.png'; // Default
                              label = entry.key;
                              subtitle = 'View result online';
                            }
                          }
                          // KU specific styling
                          else if (widget.category.name == 'KU') {
                            if (entry.key == 'Official Website') {
                              buttonColor = const Color(0xFF009688); // Teal
                              imagePath = 'assets/images/ku_results.png';
                              label = 'Official Site';
                              subtitle =
                                  'Kathmandu University official results';
                            } else {
                              buttonColor =
                                  Theme.of(context).colorScheme.primary;
                              imagePath =
                                  'assets/images/official site.png'; // Default
                              label = entry.key;
                              subtitle = 'View result online';
                            }
                          }
                          // PU specific styling
                          else if (widget.category.name == 'PU') {
                            if (entry.key == 'Official Website') {
                              buttonColor = const Color(
                                0xFF8BC34A,
                              ); // Light Green
                              imagePath = 'assets/images/pu_results.png';
                              label = 'Official Site';
                              subtitle =
                                  'Purbanchal University official results';
                            } else {
                              buttonColor =
                                  Theme.of(context).colorScheme.primary;
                              imagePath =
                                  'assets/images/official site.png'; // Default
                              label = entry.key;
                              subtitle = 'View result online';
                            }
                          }
                          // CTEVT specific styling
                          else if (widget.category.name == 'CTEVT') {
                            if (entry.key == 'Official Website') {
                              buttonColor = const Color(0xFFFF9800); // Orange
                              imagePath = 'assets/images/ctevt_results.png';
                              label = 'Official Site';
                              subtitle = 'CTEVT official results';
                            } else {
                              buttonColor =
                                  Theme.of(context).colorScheme.primary;
                              imagePath =
                                  'assets/images/official site.png'; // Default
                              label = entry.key;
                              subtitle = 'View result online';
                            }
                          }
                          // DV specific styling
                          else if (widget.category.name == 'DV') {
                            if (entry.key == 'Reliable Website') {
                              buttonColor = const Color(0xFF4CAF50); // Green
                              imagePath = 'assets/images/dv_lottery_result.png';
                              label = 'DV Lottery';
                              subtitle = 'Check lottery status';
                            } else {
                              buttonColor =
                                  Theme.of(context).colorScheme.primary;
                              imagePath =
                                  'assets/images/official site.png'; // Default
                              label = entry.key;
                              subtitle = 'View result online';
                            }
                          }
                          // IPO specific styling
                          else if (widget.category.name == 'IPO') {
                            if (entry.key == 'CDSC IPO Result') {
                              buttonColor = const Color(0xFFE91E63); // Pink
                              imagePath = 'assets/cdsc_ipo_results_new.png';
                              label = 'CDSC IPO Result';
                              subtitle = 'Check IPO allotment status';
                            } else {
                              buttonColor =
                                  Theme.of(context).colorScheme.primary;
                              imagePath =
                                  'assets/images/official site.png'; // Default
                              label = entry.key;
                              subtitle = 'View result online';
                            }
                          }

                          return Card(
                            margin: const EdgeInsets.only(bottom: 12),
                            elevation: 2,
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(12),
                            ),
                            child: InkWell(
                              onTap:
                                  () => _openWebView(
                                    context,
                                    widget.category.name == 'HSEB'
                                        ? 'Higher Secondary Education Board Result - $label'
                                        : '${widget.category.name} - $label',
                                    entry.value,
                                  ),
                              borderRadius: BorderRadius.circular(12),
                              child: Padding(
                                padding: const EdgeInsets.all(16.0),
                                child: Row(
                                  children: [
                                    // Image asset - increased size for CDSC IPO (50% larger)
                                    Container(
                                      width: entry.key == 'CDSC IPO Result' ? 60 : 60,
                                      height: entry.key == 'CDSC IPO Result' ? 60 : 60,
                                      decoration: BoxDecoration(
                                        color: Colors.white,
                                        borderRadius: BorderRadius.circular(12),
                                        boxShadow: [
                                          BoxShadow(
                                            color: buttonColor.withAlpha(40),
                                            blurRadius: 8,
                                            offset: const Offset(0, 2),
                                          ),
                                        ],
                                      ),
                                      padding: const EdgeInsets.all(8),
                                      child: Image.asset(
                                        imagePath,
                                        fit: BoxFit.contain,
                                      ),
                                    ),
                                    const SizedBox(width: 16),
                                    // Text content
                                    Expanded(
                                      child: Column(
                                        crossAxisAlignment:
                                            CrossAxisAlignment.start,
                                        children: [
                                          Text(
                                            label,
                                            style: GoogleFonts.poppins(
                                              fontWeight: FontWeight.bold,
                                              fontSize: 18,
                                              color:
                                                  Theme.of(
                                                            context,
                                                          ).brightness ==
                                                          Brightness.dark
                                                      ? Colors.white
                                                      : buttonColor,
                                            ),
                                          ),
                                          const SizedBox(height: 4),
                                          Text(
                                            subtitle,
                                            style: GoogleFonts.poppins(
                                              fontSize: 14,
                                              fontWeight: FontWeight.w500,
                                              color:
                                                  Theme.of(
                                                            context,
                                                          ).brightness ==
                                                          Brightness.dark
                                                      ? Colors.white70
                                                      : buttonColor.withAlpha(
                                                        204,
                                                      ), // 0.8 * 255 = 204
                                            ),
                                          ),
                                        ],
                                      ),
                                    ),
                                    Icon(
                                      Icons.arrow_forward_ios,
                                      color:
                                          Theme.of(context).brightness ==
                                                  Brightness.dark
                                              ? Colors.white70
                                              : buttonColor,
                                      size: 16,
                                    ),
                                  ],
                                ),
                              ),
                            ),
                          );
                        }).toList(),
                  )
                else
                  // Default list view for other categories
                  ListView.builder(
                    shrinkWrap: true,
                    physics: const NeverScrollableScrollPhysics(),
                    itemCount: widget.category.resultUrls.length,
                    itemBuilder: (context, index) {
                      final entry = widget.category.resultUrls.entries
                          .elementAt(index);

                      // Default styling
                      final buttonColor = Theme.of(context).colorScheme.primary;
                      final subtitle = 'View result online';
                      final iconData = Icons.open_in_new;

                      return Card(
                        margin: const EdgeInsets.only(bottom: 8),
                        child: ListTile(
                          title: Text(
                            entry.key,
                            style: GoogleFonts.poppins(
                              fontWeight: FontWeight.bold,
                              fontSize: 18,
                              color:
                                  Theme.of(context).brightness ==
                                          Brightness.dark
                                      ? Colors.white
                                      : buttonColor,
                            ),
                          ),
                          subtitle: Text(
                            subtitle,
                            style: GoogleFonts.poppins(
                              fontSize: 14,
                              fontWeight: FontWeight.w500,
                              color:
                                  Theme.of(context).brightness ==
                                          Brightness.dark
                                      ? Colors.white70
                                      : buttonColor.withAlpha(
                                        204,
                                      ), // 0.8 * 255 = 204
                            ),
                          ),
                          leading: CircleAvatar(
                            backgroundColor:
                                Theme.of(context).brightness == Brightness.dark
                                    ? buttonColor.withAlpha(80)
                                    : buttonColor.withAlpha(
                                      51,
                                    ), // 0.2 * 255 = 51
                            child: Icon(
                              iconData,
                              color:
                                  Theme.of(context).brightness ==
                                          Brightness.dark
                                      ? Colors.white
                                      : buttonColor,
                            ),
                          ),
                          trailing: Icon(
                            Icons.arrow_forward_ios,
                            color:
                                Theme.of(context).brightness == Brightness.dark
                                    ? Colors.white70
                                    : buttonColor,
                            size: 16,
                          ),
                          onTap:
                              () => _openWebView(
                                context,
                                '${widget.category.name} - ${entry.key}',
                                entry.value,
                              ),
                        ),
                      );
                    },
                  ),

                // SMS Support Section
                if (widget.category.hasSmsSupport) ...[
                  const SizedBox(height: 16),

                  // Custom University SMS Methods
                  if (widget.category.name == 'TU') ...[
                    Card(
                      margin: const EdgeInsets.only(bottom: 16),
                      child: Padding(
                        padding: const EdgeInsets.all(16.0),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            // Select Category
                            Text(
                              'Select Category:',
                              style: Theme.of(
                                context,
                              ).textTheme.titleMedium?.copyWith(
                                fontWeight: FontWeight.bold,
                                color:
                                    widget.category.name == 'TU'
                                        ? const Color(
                                          0xFF673AB7,
                                        ) // Deep Purple for TU
                                        : widget.category.name == 'KU'
                                        ? const Color(0xFF009688) // Teal for KU
                                        : const Color(
                                          0xFF8BC34A,
                                        ), // Light Green for PU
                              ),
                            ),
                            const SizedBox(height: 12),

                            // Category Buttons
                            Container(
                              height: 50,
                              margin: const EdgeInsets.only(bottom: 16),
                              child: SingleChildScrollView(
                                scrollDirection: Axis.horizontal,
                                child: Row(
                                  children: [
                                    for (final categoryOption in [
                                      'BBS',
                                      'B.Sc.',
                                      'B.Ed.',
                                      'BA',
                                      'MBS',
                                      'MA',
                                      'M.Ed.',
                                      'M.Sc.',
                                      'BBA',
                                      'BCA',
                                      'BIM',
                                      'BSW',
                                      'BHCM',
                                      'B.Sc. CSIT',
                                      'LLB',
                                    ])
                                      Padding(
                                        padding: const EdgeInsets.only(
                                          right: 8,
                                        ),
                                        child: ElevatedButton(
                                          style: ElevatedButton.styleFrom(
                                            backgroundColor:
                                                (widget.category.name == 'TU' &&
                                                            selectedTUCategory ==
                                                                categoryOption) ||
                                                        (widget.category.name ==
                                                                'KU' &&
                                                            selectedKUCategory ==
                                                                categoryOption) ||
                                                        (widget.category.name ==
                                                                'PU' &&
                                                            selectedPUCategory ==
                                                                categoryOption)
                                                    ? widget.category.name ==
                                                            'TU'
                                                        ? const Color(
                                                          0xFF4527A0,
                                                        ) // Darker purple for TU
                                                        : widget
                                                                .category
                                                                .name ==
                                                            'KU'
                                                        ? const Color(
                                                          0xFF00796B,
                                                        ) // Darker teal for KU
                                                        : const Color(
                                                          0xFF689F38,
                                                        ) // Darker green for PU
                                                    : widget.category.name ==
                                                        'TU'
                                                    ? const Color(
                                                      0xFF673AB7,
                                                    ) // Purple for TU
                                                    : widget.category.name ==
                                                        'KU'
                                                    ? const Color(
                                                      0xFF009688,
                                                    ) // Teal for KU
                                                    : const Color(
                                                      0xFF8BC34A,
                                                    ), // Light Green for PU
                                            foregroundColor: Colors.white,
                                            shape: RoundedRectangleBorder(
                                              borderRadius:
                                                  BorderRadius.circular(20),
                                            ),
                                          ),
                                          onPressed: () {
                                            setState(() {
                                              if (widget.category.name ==
                                                  'TU') {
                                                selectedTUCategory =
                                                    categoryOption;
                                                selectedTUYear =
                                                    null; // Reset year when category changes
                                              } else if (widget.category.name ==
                                                  'KU') {
                                                selectedKUCategory =
                                                    categoryOption;
                                                selectedKUYear =
                                                    null; // Reset year when category changes
                                              } else if (widget.category.name ==
                                                  'PU') {
                                                selectedPUCategory =
                                                    categoryOption;
                                                selectedPUYear =
                                                    null; // Reset year when category changes
                                              }
                                            });
                                          },
                                          child: Text(categoryOption),
                                        ),
                                      ),
                                  ],
                                ),
                              ),
                            ),

                            // Year Selection (only show if category is selected)
                            if ((widget.category.name == 'TU' &&
                                    selectedTUCategory != null) ||
                                (widget.category.name == 'KU' &&
                                    selectedKUCategory != null) ||
                                (widget.category.name == 'PU' &&
                                    selectedPUCategory != null)) ...[
                              Text(
                                'Select Year:',
                                style: Theme.of(
                                  context,
                                ).textTheme.titleMedium?.copyWith(
                                  fontWeight: FontWeight.bold,
                                  color:
                                      widget.category.name == 'TU'
                                          ? const Color(
                                            0xFF673AB7,
                                          ) // Deep Purple for TU
                                          : widget.category.name == 'KU'
                                          ? const Color(
                                            0xFF009688,
                                          ) // Teal for KU
                                          : const Color(
                                            0xFF8BC34A,
                                          ), // Light Green for PU
                                ),
                              ),
                              const SizedBox(height: 12),

                              // Year Buttons
                              Container(
                                height: 50,
                                margin: const EdgeInsets.only(bottom: 16),
                                child: Row(
                                  mainAxisAlignment:
                                      MainAxisAlignment.spaceEvenly,
                                  children: [
                                    for (final yearOption in [
                                      '1st Year',
                                      '2nd Year',
                                      '3rd Year',
                                      '4th Year',
                                    ])
                                      Padding(
                                        padding: const EdgeInsets.only(
                                          right: 8,
                                        ),
                                        child: ElevatedButton(
                                          style: ElevatedButton.styleFrom(
                                            backgroundColor:
                                                (widget.category.name == 'TU' &&
                                                            selectedTUYear ==
                                                                yearOption) ||
                                                        (widget.category.name ==
                                                                'KU' &&
                                                            selectedKUYear ==
                                                                yearOption) ||
                                                        (widget.category.name ==
                                                                'PU' &&
                                                            selectedPUYear ==
                                                                yearOption)
                                                    ? widget.category.name ==
                                                            'TU'
                                                        ? const Color(
                                                          0xFF4527A0,
                                                        ) // Darker purple for TU
                                                        : widget
                                                                .category
                                                                .name ==
                                                            'KU'
                                                        ? const Color(
                                                          0xFF00796B,
                                                        ) // Darker teal for KU
                                                        : const Color(
                                                          0xFF689F38,
                                                        ) // Darker green for PU
                                                    : widget.category.name ==
                                                        'TU'
                                                    ? const Color(
                                                      0xFF673AB7,
                                                    ) // Purple for TU
                                                    : widget.category.name ==
                                                        'KU'
                                                    ? const Color(
                                                      0xFF009688,
                                                    ) // Teal for KU
                                                    : const Color(
                                                      0xFF8BC34A,
                                                    ), // Light Green for PU
                                            foregroundColor: Colors.white,
                                            shape: RoundedRectangleBorder(
                                              borderRadius:
                                                  BorderRadius.circular(20),
                                            ),
                                          ),
                                          onPressed: () {
                                            setState(() {
                                              if (widget.category.name ==
                                                  'TU') {
                                                selectedTUYear = yearOption;
                                              } else if (widget.category.name ==
                                                  'KU') {
                                                selectedKUYear = yearOption;
                                              } else if (widget.category.name ==
                                                  'PU') {
                                                selectedPUYear = yearOption;
                                              }
                                            });
                                          },
                                          child: Text(yearOption),
                                        ),
                                      ),
                                  ],
                                ),
                              ),
                            ],

                            // OpenSMS Button (only show if both category and year are selected)
                            if ((widget.category.name == 'TU' &&
                                    selectedTUCategory != null &&
                                    selectedTUYear != null) ||
                                (widget.category.name == 'KU' &&
                                    selectedKUCategory != null &&
                                    selectedKUYear != null) ||
                                (widget.category.name == 'PU' &&
                                    selectedPUCategory != null &&
                                    selectedPUYear != null)) ...[
                              const SizedBox(height: 16),

                              // Format Message Preview
                              Container(
                                margin: const EdgeInsets.symmetric(vertical: 8),
                                padding: const EdgeInsets.all(12),
                                decoration: BoxDecoration(
                                  color:
                                      widget.category.name == 'TU'
                                          ? const Color(0xFF673AB7).withAlpha(
                                            25,
                                          ) // Purple for TU
                                          : widget.category.name == 'KU'
                                          ? const Color(0xFF009688).withAlpha(
                                            25,
                                          ) // Teal for KU
                                          : const Color(
                                            0xFF8BC34A,
                                          ).withAlpha(25), // Light Green for PU
                                  borderRadius: BorderRadius.circular(8),
                                ),
                                child: Column(
                                  children: [
                                    Text(
                                      'Your message will be:',
                                      style:
                                          Theme.of(
                                            context,
                                          ).textTheme.bodyMedium,
                                      textAlign: TextAlign.center,
                                    ),
                                    const SizedBox(height: 8),
                                    Text(
                                      widget.category.name == 'TU'
                                          ? '${selectedTUYear!.split(' ')[0][0]}${selectedTUCategory!.replaceAll('.', '').replaceAll(' ', '')} <symbol number>'
                                          : widget.category.name == 'KU'
                                          ? '${selectedKUYear!.split(' ')[0][0]}${selectedKUCategory!.replaceAll('.', '').replaceAll(' ', '')} <symbol number>'
                                          : '${selectedPUYear!.split(' ')[0][0]}${selectedPUCategory!.replaceAll('.', '').replaceAll(' ', '')} <symbol number>',
                                      style: Theme.of(
                                        context,
                                      ).textTheme.bodyMedium?.copyWith(
                                        fontWeight: FontWeight.bold,
                                        fontFamily: 'monospace',
                                      ),
                                      textAlign: TextAlign.center,
                                    ),
                                    const SizedBox(height: 8),
                                    Text(
                                      'Send to 33624',
                                      style:
                                          Theme.of(
                                            context,
                                          ).textTheme.bodyMedium,
                                      textAlign: TextAlign.center,
                                    ),
                                  ],
                                ),
                              ),

                              const SizedBox(height: 16),

                              // OpenSMS Button
                              SizedBox(
                                width: double.infinity,
                                child: ElevatedButton.icon(
                                  icon: const Icon(Icons.sms),
                                  label: const Text('OpenSMS'),
                                  style: ElevatedButton.styleFrom(
                                    backgroundColor:
                                        widget.category.name == 'TU'
                                            ? const Color(
                                              0xFF673AB7,
                                            ) // Purple for TU
                                            : widget.category.name == 'KU'
                                            ? const Color(
                                              0xFF009688,
                                            ) // Teal for KU
                                            : const Color(
                                              0xFF8BC34A,
                                            ), // Light Green for PU
                                    foregroundColor: Colors.white,
                                    padding: const EdgeInsets.symmetric(
                                      vertical: 12,
                                    ),
                                    textStyle: const TextStyle(
                                      fontSize: 16,
                                      fontWeight: FontWeight.bold,
                                    ),
                                  ),
                                  onPressed: () async {
                                    // Format the category code (remove dots and spaces)
                                    String categoryCode = '';
                                    String yearNumber = '';

                                    if (widget.category.name == 'TU') {
                                      categoryCode = selectedTUCategory!
                                          .replaceAll('.', '')
                                          .replaceAll(' ', '');
                                      yearNumber =
                                          selectedTUYear!.split(' ')[0][0];
                                    } else if (widget.category.name == 'KU') {
                                      categoryCode = selectedKUCategory!
                                          .replaceAll('.', '')
                                          .replaceAll(' ', '');
                                      yearNumber =
                                          selectedKUYear!.split(' ')[0][0];
                                    } else if (widget.category.name == 'PU') {
                                      categoryCode = selectedPUCategory!
                                          .replaceAll('.', '')
                                          .replaceAll(' ', '');
                                      yearNumber =
                                          selectedPUYear!.split(' ')[0][0];
                                    }

                                    final uri = Uri.parse(
                                      'sms:33624?body=$yearNumber$categoryCode ',
                                    );
                                    try {
                                      await launchUrl(
                                        uri,
                                        mode: LaunchMode.externalApplication,
                                      );
                                    } catch (e) {
                                      if (context.mounted) {
                                        ScaffoldMessenger.of(
                                          context,
                                        ).showSnackBar(
                                          const SnackBar(
                                            content: Text(
                                              'Could not open SMS app',
                                            ),
                                            duration: Duration(seconds: 2),
                                          ),
                                        );
                                      }
                                    }
                                  },
                                ),
                              ),

                              // Message below OpenSMS button
                              Padding(
                                padding: const EdgeInsets.only(top: 8.0),
                                child: Text(
                                  "Click 'OpenSMS' to send your result query via SMS.",
                                  style: Theme.of(
                                    context,
                                  ).textTheme.bodyMedium?.copyWith(
                                    color: Colors.grey[700],
                                    fontStyle: FontStyle.italic,
                                  ),
                                  textAlign: TextAlign.center,
                                ),
                              ),
                            ],

                            const SizedBox(height: 24),

                            // Help message at the bottom
                            Container(
                              padding: const EdgeInsets.all(12),
                              decoration: BoxDecoration(
                                color: Colors.grey.withAlpha(25),
                                borderRadius: BorderRadius.circular(8),
                                border: Border.all(
                                  color: Colors.grey.withAlpha(75),
                                ),
                              ),
                              child: Row(
                                children: [
                                  Icon(
                                    Icons.help_outline,
                                    color:
                                        Theme.of(context).colorScheme.primary,
                                  ),
                                  const SizedBox(width: 12),
                                  Expanded(
                                    child: Text(
                                      'If you have any doubts regarding this tutorial, feel free to ask.',
                                      style:
                                          Theme.of(
                                            context,
                                          ).textTheme.bodyMedium,
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                  ]
                  // Default SMS Method for other categories
                  else ...[
                    Text(
                      'SMS Method',
                      style: Theme.of(context).textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 8),

                    // NTC SMS Card
                    Card(
                      margin: const EdgeInsets.only(bottom: 8),
                      child: Padding(
                        padding: const EdgeInsets.all(16.0),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Row(
                              children: [
                                Icon(
                                  Icons.sms,
                                  color: Theme.of(context).colorScheme.primary,
                                ),
                                const SizedBox(width: 8),
                                Text(
                                  'NTC SMS',
                                  style: Theme.of(context).textTheme.titleSmall
                                      ?.copyWith(fontWeight: FontWeight.bold),
                                ),
                              ],
                            ),
                            const SizedBox(height: 8),
                            Text(
                              'Type ${widget.category.smsInfo!.format} and send to ${widget.category.smsInfo!.sendTo}',
                              style: Theme.of(context).textTheme.bodyMedium,
                            ),
                            const SizedBox(height: 12),
                            SizedBox(
                              width: double.infinity,
                              child: ElevatedButton.icon(
                                icon: const Icon(Icons.sms),
                                label: const Text('Open SMS App'),
                                style: ElevatedButton.styleFrom(
                                  backgroundColor:
                                      Theme.of(context).colorScheme.secondary,
                                ),
                                onPressed: () async {
                                  // Extract the format prefix (e.g., "SEE" from "SEE <SymbolNumber>")
                                  final formatPrefix =
                                      widget.category.smsInfo!.format.split(
                                        ' ',
                                      )[0];
                                  final uri = Uri.parse(
                                    'sms:${widget.category.smsInfo!.sendTo}?body=$formatPrefix ',
                                  );
                                  try {
                                    await launchUrl(
                                      uri,
                                      mode: LaunchMode.externalApplication,
                                    );
                                  } catch (e) {
                                    if (context.mounted) {
                                      ScaffoldMessenger.of(
                                        context,
                                      ).showSnackBar(
                                        const SnackBar(
                                          content: Text(
                                            'Could not open SMS app',
                                          ),
                                          duration: Duration(seconds: 2),
                                        ),
                                      );
                                    }
                                  }
                                },
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),

                    // Ncell SMS Card (if available)
                    if (widget.category.smsInfo!.alternateSendTo != null)
                      Card(
                        margin: const EdgeInsets.only(bottom: 8),
                        child: Padding(
                          padding: const EdgeInsets.all(16.0),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Row(
                                children: [
                                  Icon(
                                    Icons.sms,
                                    color:
                                        Theme.of(context).colorScheme.primary,
                                  ),
                                  const SizedBox(width: 8),
                                  Text(
                                    'Ncell SMS',
                                    style: Theme.of(context)
                                        .textTheme
                                        .titleSmall
                                        ?.copyWith(fontWeight: FontWeight.bold),
                                  ),
                                ],
                              ),
                              const SizedBox(height: 8),
                              Text(
                                'Type ${widget.category.smsInfo!.alternateFormat} and send to ${widget.category.smsInfo!.alternateSendTo}',
                                style: Theme.of(context).textTheme.bodyMedium,
                              ),
                              const SizedBox(height: 12),
                              SizedBox(
                                width: double.infinity,
                                child: ElevatedButton.icon(
                                  icon: const Icon(Icons.sms),
                                  label: const Text('Open SMS App'),
                                  style: ElevatedButton.styleFrom(
                                    backgroundColor:
                                        Theme.of(context).colorScheme.secondary,
                                  ),
                                  onPressed: () async {
                                    // Extract the format prefix (e.g., "SEE" from "SEE <SymbolNumber>")
                                    final formatPrefix =
                                        widget
                                            .category
                                            .smsInfo!
                                            .alternateFormat!
                                            .split(' ')[0];
                                    final uri = Uri.parse(
                                      'sms:${widget.category.smsInfo!.alternateSendTo}?body=$formatPrefix ',
                                    );
                                    try {
                                      await launchUrl(
                                        uri,
                                        mode: LaunchMode.externalApplication,
                                      );
                                    } catch (e) {
                                      if (context.mounted) {
                                        ScaffoldMessenger.of(
                                          context,
                                        ).showSnackBar(
                                          const SnackBar(
                                            content: Text(
                                              'Could not open SMS app',
                                            ),
                                            duration: Duration(seconds: 2),
                                          ),
                                        );
                                      }
                                    }
                                  },
                                ),
                              ),
                            ],
                          ),
                        ),
                      ),
                  ],

                  // IVR Method Section (if available)
                  if (widget.category.smsInfo!.ivrNumber != null) ...[
                    const SizedBox(height: 16),

                    Text(
                      'IVR Method',
                      style: Theme.of(context).textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 8),

                    Card(
                      margin: const EdgeInsets.only(bottom: 8),
                      child: Padding(
                        padding: const EdgeInsets.all(16.0),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Row(
                              children: [
                                Icon(
                                  Icons.phone,
                                  color: Theme.of(context).colorScheme.primary,
                                ),
                                const SizedBox(width: 8),
                                Text(
                                  'Call System',
                                  style: Theme.of(context).textTheme.titleSmall
                                      ?.copyWith(fontWeight: FontWeight.bold),
                                ),
                              ],
                            ),
                            const SizedBox(height: 8),
                            Text(
                              'Dial ${widget.category.smsInfo!.ivrNumber} from NTC mobile or landline and follow the instructions',
                              style: Theme.of(context).textTheme.bodyMedium,
                            ),
                            const SizedBox(height: 12),
                            SizedBox(
                              width: double.infinity,
                              child: ElevatedButton.icon(
                                icon: const Icon(Icons.call),
                                label: const Text('Call Now'),
                                style: ElevatedButton.styleFrom(
                                  backgroundColor:
                                      Theme.of(context).colorScheme.secondary,
                                ),
                                onPressed: () async {
                                  final uri = Uri.parse(
                                    'tel:${widget.category.smsInfo!.ivrNumber}',
                                  );
                                  try {
                                    await launchUrl(
                                      uri,
                                      mode: LaunchMode.externalApplication,
                                    );
                                  } catch (e) {
                                    if (context.mounted) {
                                      ScaffoldMessenger.of(
                                        context,
                                      ).showSnackBar(
                                        const SnackBar(
                                          content: Text(
                                            'Could not launch phone dialer',
                                          ),
                                          duration: Duration(seconds: 2),
                                        ),
                                      );
                                    }
                                  }
                                },
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),

                    // Grading System Section
                    if (widget.category.name == 'SEE') ...[
                      const SizedBox(height: 24),

                      Text(
                        'SEE Grading System',
                        style: Theme.of(context).textTheme.titleMedium
                            ?.copyWith(fontWeight: FontWeight.bold),
                      ),
                      const SizedBox(height: 12),

                      ClipRRect(
                        borderRadius: BorderRadius.circular(12),
                        child: Image.asset(
                          'assets/images/see_grading_system_new .png',
                          fit: BoxFit.contain,
                          width: double.infinity,
                        ),
                      ),
                    ] else if (widget.category.name == 'HSEB') ...[
                      const SizedBox(height: 24),

                      Text(
                        'HSEB Grading System',
                        style: Theme.of(context).textTheme.titleMedium
                            ?.copyWith(fontWeight: FontWeight.bold),
                      ),
                      const SizedBox(height: 12),

                      ClipRRect(
                        borderRadius: BorderRadius.circular(12),
                        child: Image.asset(
                          'assets/images/hseb_grading_system.png',
                          fit: BoxFit.contain,
                          width: double.infinity,
                        ),
                      ),
                    ],
                  ],
                ],

                const SizedBox(height: 16),

                // IPO Notification Section (only for IPO category)
                if (widget.category.name == 'IPO') ...[
                  const SizedBox(height: 16),

                  Text(
                    'Notifications',
                    style: Theme.of(context).textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 8),

                  Card(
                    margin: const EdgeInsets.only(bottom: 16),
                    child: Padding(
                      padding: const EdgeInsets.all(16.0),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            'Get notified when new IPO results are published',
                            style: Theme.of(context).textTheme.bodyLarge,
                          ),
                          const SizedBox(height: 16),
                          Column(
                            children: [
                              SizedBox(
                                width: double.infinity,
                                child: ElevatedButton.icon(
                                  icon: const Icon(Icons.notifications_active),
                                  label: const Text('Enable Notifications'),
                                  style: ElevatedButton.styleFrom(
                                    backgroundColor: const Color(
                                      0xFFE91E63,
                                    ), // Pink
                                    foregroundColor: Colors.white,
                                  ),
                                  onPressed: () async {
                                    // Request notification permissions
                                    await Provider.of<NotificationProvider>(
                                      context,
                                      listen: false,
                                    ).initialize();

                                    // Subscribe to IPO topic
                                    await Provider.of<NotificationProvider>(
                                      context,
                                      listen: false,
                                    ).toggleNotifications(true);

                                    // Show confirmation
                                    if (context.mounted) {
                                      ScaffoldMessenger.of(
                                        context,
                                      ).showSnackBar(
                                        const SnackBar(
                                          content: Text(
                                            'IPO result notifications enabled',
                                          ),
                                          duration: Duration(seconds: 2),
                                        ),
                                      );
                                    }
                                  },
                                ),
                              ),
                              const SizedBox(height: 12),
                              SizedBox(
                                width: double.infinity,
                                child: OutlinedButton.icon(
                                  icon: const Icon(Icons.refresh),
                                  label: const Text('Check for New Results'),
                                  onPressed: () async {
                                    // Show loading indicator
                                    ScaffoldMessenger.of(context).showSnackBar(
                                      const SnackBar(
                                        content: Text(
                                          'Checking for new IPO results...',
                                        ),
                                        duration: Duration(seconds: 2),
                                      ),
                                    );

                                    // Simulate checking for new IPO results
                                    await Provider.of<NotificationProvider>(
                                      context,
                                      listen: false,
                                    ).simulateNewNotification();

                                    // Show confirmation
                                    if (context.mounted) {
                                      ScaffoldMessenger.of(
                                        context,
                                      ).showSnackBar(
                                        const SnackBar(
                                          content: Text(
                                            'Checked for new IPO results',
                                          ),
                                          duration: Duration(seconds: 2),
                                        ),
                                      );
                                    }
                                  },
                                ),
                              ),
                            ],
                          ),
                        ],
                      ),
                    ),
                  ),
                ],

                // Help Section
                Card(
                  margin: const EdgeInsets.symmetric(vertical: 8),
                  child: Padding(
                    padding: const EdgeInsets.all(16.0),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          'Having trouble accessing results?',
                          style: Theme.of(context).textTheme.titleMedium,
                        ),
                        const SizedBox(height: 12),
                        SizedBox(
                          width: double.infinity,
                          child: OutlinedButton.icon(
                            icon: const Icon(Icons.search),
                            label: const Text('Search on Google'),
                            onPressed: () => _searchOnGoogle(context),
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    ),
    );
  }
}
