import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import '../services/chrome_custom_tabs_service.dart';

class CTEVTResultsDetailPage extends StatefulWidget {
  const CTEVTResultsDetailPage({super.key});

  @override
  State<CTEVTResultsDetailPage> createState() => _CTEVTResultsDetailPageState();
}

class _CTEVTResultsDetailPageState extends State<CTEVTResultsDetailPage> {
  Future<void> _launchChromeCustomTab(String url, String title) async {
    try {
      if (url.contains('ctevt.org.np')) {
        await ChromeCustomTabsService.launchCTEVTResults();
      } else {
        await ChromeCustomTabsService.launch(url, title: title);
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Could not open $title: ${e.toString()}')),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('CTEVT Results'),
        backgroundColor: Colors.brown.shade700,
        foregroundColor: Colors.white,
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header with CTEVT logo
            Card(
              elevation: 4,
              child: Padding(
                padding: const EdgeInsets.all(20.0),
                child: Row(
                  children: [
                    Image.asset(
                      'assets/images/ctevt_icon.png',
                      width: 60,
                      height: 60,
                      errorBuilder: (context, error, stackTrace) {
                        return Icon(
                          Icons.engineering,
                          size: 60,
                          color: Colors.brown.shade700,
                        );
                      },
                    ),
                    const SizedBox(width: 16),
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            'CTEVT',
                            style: GoogleFonts.poppins(
                              fontSize: 20,
                              fontWeight: FontWeight.bold,
                              color: Colors.brown.shade700,
                            ),
                          ),
                          const SizedBox(height: 4),
                          Text(
                            'Council for Technical Education and Vocational Training',
                            style: GoogleFonts.poppins(
                              fontSize: 12,
                              color: Colors.grey.shade600,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
            ),

            const SizedBox(height: 24),

            // Official Sites Section
            _buildSectionTitle('Official Sites', Icons.public),
            const SizedBox(height: 16),
            _buildWebsiteCard(
              'CTEVT Official Website',
              'https://ctevt.org.np',
              Icons.engineering,
              Colors.brown,
              () => ChromeCustomTabsService.launchCTEVTResults(),
            ),

            const SizedBox(height: 32),

            // Information Section
            _buildSectionTitle('Information', Icons.info_outline),
            const SizedBox(height: 16),
            Card(
              elevation: 2,
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'About CTEVT Results',
                      style: GoogleFonts.poppins(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                        color: Colors.brown.shade700,
                      ),
                    ),
                    const SizedBox(height: 12),
                    Text(
                      '• CTEVT is the apex body for technical and vocational education in Nepal\n'
                      '• Results are published on the official CTEVT website\n'
                      '• Check your results using your registration number\n'
                      '• Results include diploma, certificate, and skill-based programs\n'
                      '• Contact your respective institute for any queries',
                      style: GoogleFonts.poppins(
                        fontSize: 14,
                        color: Colors.grey.shade700,
                      ),
                    ),
                  ],
                ),
              ),
            ),

            const SizedBox(height: 24),

            // Quick Links Section
            _buildSectionTitle('Quick Links', Icons.link),
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: _buildQuickLinkCard(
                    'Admit Card',
                    Icons.card_membership,
                    Colors.orange,
                    () => _launchChromeCustomTab('https://ctevt.org.np/', 'CTEVT Admit Card'),
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: _buildQuickLinkCard(
                    'Exam Schedule',
                    Icons.schedule,
                    Colors.blue,
                    () => _launchChromeCustomTab('https://ctevt.org.np/', 'CTEVT Exam Schedule'),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSectionTitle(String title, IconData icon) {
    return Row(
      children: [
        Icon(icon, color: Colors.brown.shade700, size: 24),
        const SizedBox(width: 8),
        Text(
          title,
          style: GoogleFonts.poppins(
            fontSize: 18,
            fontWeight: FontWeight.bold,
            color: Colors.brown.shade700,
          ),
        ),
      ],
    );
  }

  Widget _buildWebsiteCard(String title, String url, IconData icon, Color color, [VoidCallback? customLaunch]) {
    return Card(
      elevation: 2,
      child: InkWell(
        onTap: () async {
          try {
            if (customLaunch != null) {
              customLaunch();
            } else {
              await _launchChromeCustomTab(url, title);
            }
          } catch (e) {
            if (mounted) {
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(content: Text('Could not open $title: ${e.toString()}')),
              );
            }
          }
        },
        borderRadius: BorderRadius.circular(12),
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Row(
            children: [
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: color.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Icon(icon, color: color, size: 24),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      title,
                      style: GoogleFonts.poppins(
                        fontSize: 16,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      'Tap to open in browser',
                      style: GoogleFonts.poppins(
                        fontSize: 12,
                        color: Colors.grey.shade600,
                      ),
                    ),
                  ],
                ),
              ),
              const Icon(Icons.arrow_forward_ios, size: 16),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildQuickLinkCard(String title, IconData icon, Color color, VoidCallback onTap) {
    return Card(
      elevation: 2,
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(12),
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            children: [
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: color.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Icon(icon, color: color, size: 32),
              ),
              const SizedBox(height: 12),
              Text(
                title,
                textAlign: TextAlign.center,
                style: GoogleFonts.poppins(
                  fontSize: 14,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
