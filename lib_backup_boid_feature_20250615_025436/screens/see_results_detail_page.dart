import 'package:flutter/material.dart';
import 'package:url_launcher/url_launcher.dart';
import 'package:google_fonts/google_fonts.dart';
import '../services/chrome_custom_tabs_service.dart';

class SEEResultsDetailPage extends StatefulWidget {
  const SEEResultsDetailPage({super.key});

  @override
  State<SEEResultsDetailPage> createState() => _SEEResultsDetailPageState();
}

class _SEEResultsDetailPageState extends State<SEEResultsDetailPage> {
  final TextEditingController _symbolController = TextEditingController();

  @override
  void dispose() {
    _symbolController.dispose();
    super.dispose();
  }

  Future<void> _launchChromeCustomTab(String url, String title) async {
    try {
      if (url.contains('ekantipur.com')) {
        await ChromeCustomTabsService.launchEKantipurResults(url, title);
      } else if (url.contains('ntc.net.np')) {
        await ChromeCustomTabsService.launchNTCPortal(url, title);
      } else if (url.contains('see.gov.np')) {
        await ChromeCustomTabsService.launchSEEResults();
      } else {
        await ChromeCustomTabsService.launch(url, title: title);
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Could not open $title: ${e.toString()}')),
        );
      }
    }
  }

  Future<void> _launchSMS(String message) async {
    final Uri smsUri = Uri(
      scheme: 'sms',
      path: '1600',
      queryParameters: {'body': message},
    );
    
    if (await canLaunchUrl(smsUri)) {
      await launchUrl(smsUri);
    } else {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('Could not open SMS app')),
        );
      }
    }
  }

  Future<void> _launchPhone(String number) async {
    final Uri phoneUri = Uri(scheme: 'tel', path: number);
    
    if (await canLaunchUrl(phoneUri)) {
      await launchUrl(phoneUri);
    } else {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('Could not open phone app')),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('SEE Results'),
        elevation: 0,
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Official Sites Section
            _buildSectionTitle('Official Sites', Icons.public),
            const SizedBox(height: 16),
            _buildWebsiteCard(
              'SEE Official Website',
              'http://see.gov.np/exam/results',
              Icons.school,
              Colors.blue,
            ),
            const SizedBox(height: 12),
            _buildWebsiteCard(
              'NTC Result Portal',
              'https://see.ntc.net.np/',
              Icons.language,
              Colors.indigo,
            ),
            const SizedBox(height: 12),
            _buildWebsiteCard(
              'eKantipur SEE Results',
              'https://results.ekantipur.com/see-results-with-marksheet.php',
              Icons.article,
              Colors.red,
            ),

            const SizedBox(height: 32),

            // SMS Result Check Section
            _buildSectionTitle('SMS Result Check', Icons.sms),
            const SizedBox(height: 16),
            _buildSMSSection(),

            const SizedBox(height: 32),

            // IVR Section
            _buildSectionTitle('Voice Call (IVR)', Icons.phone),
            const SizedBox(height: 16),
            _buildIVRSection(),
          ],
        ),
      ),
    );
  }

  Widget _buildSectionTitle(String title, IconData icon) {
    return Row(
      children: [
        Icon(icon, color: Theme.of(context).colorScheme.primary, size: 24),
        const SizedBox(width: 8),
        Text(
          title,
          style: GoogleFonts.poppins(
            fontSize: 18,
            fontWeight: FontWeight.bold,
            color: Theme.of(context).colorScheme.primary,
          ),
        ),
      ],
    );
  }

  Widget _buildWebsiteCard(String title, String url, IconData icon, Color color) {
    return Card(
      elevation: 2,
      child: InkWell(
        onTap: () => _launchChromeCustomTab(url, title),
        borderRadius: BorderRadius.circular(12),
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Row(
            children: [
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: color.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Icon(icon, color: color, size: 24),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      title,
                      style: GoogleFonts.poppins(
                        fontSize: 16,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      'Tap to open in browser',
                      style: GoogleFonts.poppins(
                        fontSize: 12,
                        color: Theme.of(context).brightness == Brightness.dark
                            ? Colors.grey.shade400
                            : Colors.grey.shade600,
                      ),
                    ),
                  ],
                ),
              ),
              const Icon(Icons.arrow_forward_ios, size: 16),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildSMSSection() {
    return Card(
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Format: SEE <Symbol Number>',
              style: GoogleFonts.poppins(
                fontSize: 16,
                fontWeight: FontWeight.w600,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              'Example: SEE 1234567D',
              style: GoogleFonts.poppins(
                fontSize: 14,
                color: Theme.of(context).brightness == Brightness.dark
                    ? Colors.grey.shade400
                    : Colors.grey.shade600,
              ),
            ),
            const SizedBox(height: 16),
            TextField(
              controller: _symbolController,
              decoration: InputDecoration(
                labelText: 'Enter Symbol Number',
                hintText: 'e.g., 1234567D',
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
                prefixIcon: const Icon(Icons.numbers),
              ),
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: ElevatedButton.icon(
                    onPressed: () {
                      if (_symbolController.text.isNotEmpty) {
                        _launchSMS('SEE ${_symbolController.text}');
                      } else {
                        ScaffoldMessenger.of(context).showSnackBar(
                          const SnackBar(content: Text('Please enter symbol number')),
                        );
                      }
                    },
                    icon: const Icon(Icons.sms),
                    label: const Text('Send SMS to 1600'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.green,
                      foregroundColor: Colors.white,
                      padding: const EdgeInsets.symmetric(vertical: 12),
                    ),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 8),
            Text(
              'Available for NTC and Ncell users',
              style: GoogleFonts.poppins(
                fontSize: 12,
                color: Theme.of(context).brightness == Brightness.dark
                    ? Colors.grey.shade400
                    : Colors.grey.shade600,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildIVRSection() {
    return Card(
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Call 1600 for Voice Results',
              style: GoogleFonts.poppins(
                fontSize: 16,
                fontWeight: FontWeight.w600,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              'Available for NTC users only',
              style: GoogleFonts.poppins(
                fontSize: 14,
                color: Theme.of(context).brightness == Brightness.dark
                    ? Colors.grey.shade400
                    : Colors.grey.shade600,
              ),
            ),
            const SizedBox(height: 16),
            SizedBox(
              width: double.infinity,
              child: ElevatedButton.icon(
                onPressed: () => _launchPhone('1600'),
                icon: const Icon(Icons.phone),
                label: const Text('Call 1600'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: Theme.of(context).colorScheme.primary,
                  foregroundColor: Colors.white,
                  padding: const EdgeInsets.symmetric(vertical: 12),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}


